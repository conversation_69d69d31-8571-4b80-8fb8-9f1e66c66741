{"name": "ulrf-vite-example", "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "dev:ssr": "npm run build && npm start", "build": "vite build && vite build --ssr src/server/entry-server.ts --outDir dist/server && cp src/index.html dist/", "start": "node dist/server/entry-server.js", "preview": "vite preview", "build:start": "npm run build && npm start"}, "dependencies": {"express": "^4.18.2"}, "devDependencies": {"@types/node": "^18.11.9", "@types/express": "^4.17.17", "@vitejs/plugin-react": "^4.0.0", "typescript": "^4.8.4", "vite": "^4.0.0", "esbuild-register": "^3.1.1"}}