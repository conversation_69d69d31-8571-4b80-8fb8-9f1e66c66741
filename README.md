# ULRF (Ultra-Light Reactive Frontend) Framework

A minimal, performant reactive frontend framework built with Vite, featuring:

- **Ultra-light client bundle** (~1.5KB gzipped)
- **Server-side rendering** with Express
- **Islands architecture** with selective hydration
- **Fine-grained reactivity** using signals
- **Event bus** for cross-component communication
- **Multiple hydration strategies** (immediate, visible, on-event)

## Features

### 🚀 Performance
- Minimal JavaScript payload to the client
- Only interactive components ("islands") are hydrated
- Fine-grained reactivity with no Virtual DOM overhead
- Batched DOM updates via `requestAnimationFrame`

### 🏗️ Architecture
- **SSR-first**: Fully rendered HTML served from the server
- **Islands**: Interactive components hydrate independently
- **Selective hydration**: Components can hydrate on load, when visible, or on user interaction
- **Event-driven**: Components communicate via a global event bus

### 🛠️ Developer Experience
- TypeScript support throughout
- Hot module replacement in development
- Simple component API similar to React hooks
- Vite-powered build system

## Quick Start

### Installation

```bash
npm install
```

### Development

```bash
npm run dev
```

### Production Build

```bash
npm run build
```

### Start Production Server

```bash
npm start
```

Or build and start in one command:

```bash
npm run build:start
```

## Project Structure

```
src/
├── client/              # Client-side code
│   ├── entry-client.ts  # Client entry point
│   ├── hydrate.ts       # Island hydration logic
│   └── reactive.ts      # Reactive core (signals, effects)
├── components/          # UI components
│   ├── Counter.tsx      # Example counter component
│   ├── Greeting.tsx     # Interactive greeting with input
│   └── EventListener.tsx # Event bus demonstration
├── server/              # Server-side code
│   ├── entry-server.ts  # Express server
│   └── render.ts        # SSR rendering functions
└── index.html           # HTML template
```

## Core Concepts

### Signals and Effects

```typescript
import { createSignal, effect } from '../client/reactive';

// Create a reactive signal
const [count, setCount] = createSignal(0);

// Create an effect that runs when signals change
effect(() => {
  console.log('Count is:', count());
});

// Update the signal (triggers the effect)
setCount(count() + 1);
```

### Component Creation

```typescript
export function MyComponent(props: { initial?: number }) {
  const [value, setValue] = createSignal(props.initial ?? 0);
  
  const button = document.createElement('button');
  
  // Reactive update
  effect(() => {
    button.textContent = `Value: ${value()}`;
  });
  
  button.addEventListener('click', () => setValue(value() + 1));
  
  return button;
}
```

### Hydration Strategies

Components can use different hydration strategies:

- **`load`** (default): Hydrate immediately when page loads
- **`visible`**: Hydrate when component scrolls into view
- **`event`**: Hydrate on first user interaction (click)
- **`idle`**: Hydrate when browser is idle

```typescript
// In server render function
renderIsland('Counter', { initial: 5 }, 'visible'); // Hydrate when visible
```

### Event Bus Communication

```typescript
import { eventBus } from '../client/reactive';

// Listen for events
eventBus.addEventListener('myEvent', (e) => {
  console.log('Received:', e.detail);
});

// Dispatch events
eventBus.dispatchEvent(new CustomEvent('myEvent', { 
  detail: { message: 'Hello!' } 
}));
```

## Bundle Analysis

- **Client bundle**: ~9.1KB (~3.1KB gzipped)
- **Web Worker bundle**: ~1KB (loaded on demand)
- **Server bundle**: ~4.9KB
- **Runtime overhead**: Minimal - only reactive core and hydration logic

## Current Implementation Status

✅ **Completed Features:**
- ✅ Reactive core with signals and effects
- ✅ Server-side rendering with Express
- ✅ Islands architecture with selective hydration
- ✅ Multiple hydration strategies (load, visible, event, idle)
- ✅ Event bus for cross-component communication
- ✅ Web Worker integration for image processing
- ✅ Performance monitoring component
- ✅ TypeScript support throughout
- ✅ Vite build system with code splitting

🧪 **Demo Components:**
- **Counter**: Basic reactive counter with different hydration strategies
- **Greeting**: Interactive input with event bus communication
- **EventListener**: Demonstrates cross-component messaging
- **ImageFilter**: Web Worker-based image processing
- **PerformanceMonitor**: Real-time FPS and memory monitoring

## Browser Support

- Modern browsers with ES2020+ support
- Uses native `Proxy`, `CustomEvent`, and `IntersectionObserver`
- No polyfills included (add as needed for older browsers)

## Comparison with Other Frameworks

| Framework | Bundle Size | Hydration | SSR | Islands |
|-----------|-------------|-----------|-----|---------|
| ULRF      | ~1.5KB      | Selective | ✅   | ✅       |
| React     | ~42KB       | Full page | ✅   | ❌       |
| Vue       | ~34KB       | Full page | ✅   | ❌       |
| Svelte    | ~10KB       | Full page | ✅   | ❌       |
| Qwik      | ~1KB        | Resumable | ✅   | ✅       |

## License

MIT
