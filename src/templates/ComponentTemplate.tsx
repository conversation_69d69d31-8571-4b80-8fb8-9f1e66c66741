// ----------------------------------
// Component Template - Copy this to create new components
// ----------------------------------

import { defineComponent, useState, useEffect, createElement, styled } from '../client/component';

// 1. Define your component props interface
interface MyComponentProps {
  title?: string;
  initialValue?: number;
  // Add more props as needed
}

// 2. Create styled elements (optional)
const Container = styled('div', {
  padding: '20px',
  border: '1px solid #ddd',
  borderRadius: '8px',
  backgroundColor: '#f9f9f9',
  maxWidth: '400px',
  margin: '0 auto'
});

const Button = styled('button', {
  padding: '10px 20px',
  fontSize: '14px',
  backgroundColor: '#007acc',
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  cursor: 'pointer'
});

// 3. Define your component
export const MyComponent = defineComponent<MyComponentProps>({
  template: (props, state, methods) => {
    // Create reactive state
    const value = useState(props.initialValue ?? 0);
    const message = useState('');
    
    // Create DOM elements
    const container = Container();
    
    const title = createElement('h3', {
      style: 'margin-top: 0; color: #333;'
    }, [props.title || 'My Component']);
    
    const display = createElement('p', {
      style: 'font-size: 18px; font-weight: bold; color: #007acc;'
    }, [() => `Value: ${value.value}`]);
    
    const input = createElement('input', {
      type: 'text',
      placeholder: 'Enter a message',
      style: 'padding: 8px; margin: 10px 0; width: 200px;'
    }) as HTMLInputElement;
    
    const button = Button({}, ['Update']);
    
    const messageDisplay = createElement('p', {
      style: 'color: #666; font-style: italic;'
    }, [() => message.value || 'No message yet']);
    
    // Add event listeners
    button.addEventListener('click', () => {
      value.value++;
      message.value = input.value;
      
      // Emit events for other components to listen
      methods.emit('componentUpdated', {
        value: value.value,
        message: message.value
      });
    });
    
    input.addEventListener('input', (e) => {
      const target = e.target as HTMLInputElement;
      message.value = target.value;
    });
    
    // Use effects for side effects
    useEffect(() => {
      console.log('Value changed to:', value.value);
    }, [() => value.value]);
    
    // Listen for events from other components
    methods.listen('globalReset', () => {
      value.value = props.initialValue ?? 0;
      message.value = '';
      input.value = '';
    });
    
    // Assemble the component
    container.appendChild(title);
    container.appendChild(display);
    container.appendChild(input);
    container.appendChild(button);
    container.appendChild(messageDisplay);
    
    return container;
  },
  
  // Optional: Called when component is mounted
  mounted: (element, props, state, methods) => {
    console.log('MyComponent mounted with props:', props);
    
    // You can add additional setup here
    // For example, focus an input, start timers, etc.
  },
  
  // Optional: Called when component is unmounted
  unmounted: () => {
    console.log('MyComponent unmounted');
    
    // Clean up resources here
    // For example, clear timers, remove global listeners, etc.
  }
});

// 4. To use this component:
// 
// a) Add to hydration registry (src/client/hydrate.ts):
//    MyComponent: (props) => MyComponent(props),
//
// b) Add to server render (src/server/render.ts):
//    renderIsland('MyComponent', { title: 'Hello', initialValue: 42 }, 'load');
//
// c) Build and test:
//    npm run build && npm start
