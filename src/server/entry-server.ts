// ----------------------------------
// src/server/entry-server.ts
// ----------------------------------
import express from 'express';
import path from 'path';
import fs from 'fs/promises';
import { fileURLToPath } from 'url';

// Import render function(s)
import { renderHomePage } from './render.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = process.env.PORT ? Number(process.env.PORT) : 3000;

// 1. Serve static assets (client bundles, CSS, images)
app.use('/assets', express.static(path.resolve(__dirname, '../client/assets')));
app.use(express.static(path.resolve(__dirname, '../client')));

// Function to find the client entry file
async function getClientEntryFile(): Promise<string> {
  try {
    const assetsDir = path.resolve(__dirname, '../client/assets');
    const files = await fs.readdir(assetsDir);
    const clientFile = files.find(file => file.startsWith('client-') && file.endsWith('.js'));
    return clientFile ? `/assets/${clientFile}` : '/assets/client.js';
  } catch (err) {
    console.warn('Could not find client entry file, using fallback');
    return '/assets/client.js';
  }
}

// 2. Handle all GET requests
app.get('*', async (req, res) => {
  try {
    // 2a. Call your render function for this route
    const appHtml = renderHomePage(req.url);

    // 2b. Load the HTML template
    const indexHtmlPath = path.resolve(__dirname, '../index.html');
    let template = await fs.readFile(indexHtmlPath, 'utf-8');

    // 2c. Get the correct client entry file
    const clientEntryFile = await getClientEntryFile();

    // 2d. Inject the rendered HTML and correct script path into the template
    let html = template.replace('<!--ssr-outlet-->', appHtml);
    html = html.replace('/src/client/entry-client.ts', clientEntryFile);

    // 2e. Send the result
    res.status(200).set({ 'Content-Type': 'text/html' }).send(html);
  } catch (err) {
    console.error(err);
    res.status(500).send('Internal Server Error');
  }
});

// 3. Start server
app.listen(port, () => {
  console.log(`ULRF Server running at http://localhost:${port}`);
});
