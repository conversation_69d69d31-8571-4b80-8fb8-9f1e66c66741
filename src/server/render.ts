// ----------------------------------
// src/server/render.ts
// ----------------------------------

interface IslandPlaceholder {
  tag: string;           // e.g. 'div'
  componentName: string; // e.g. 'Counter'
  props: Record<string, any>;
}

function renderIsland(
  componentName: string,
  props: Record<string, any>,
  hydrateStrategy: string = 'load'
): string {
  // Serialize props (e.g. `{ initial: 5 }` → `{"initial":5}`)
  const serialized = JSON.stringify(props).replace(/"/g, '&quot;');
  return `<div data-ulrf-component="${componentName}" data-props="${serialized}" data-hydrate="${hydrateStrategy}"></div>`;
}

// Example: Home page render
export function renderHomePage(url: string): string {
  // In a real app, you might inspect `url` and choose different layouts
  // For demonstration, we render various islands with different hydration strategies
  let html = '';
  html += `<header style="padding: 20px; background: #f0f0f0; text-align: center;">`;
  html += `<h1>Welcome to ULRF</h1>`;
  html += `<p>Ultra-Light Reactive Frontend Framework</p>`;
  html += `<p style="font-size: 14px; color: #666;">Demonstrating Islands Architecture with Selective Hydration</p>`;
  html += `</header>`;

  html += `<main style="padding: 20px;">`;

  // Section 1: Counter Islands
  html += `<section style="margin-bottom: 40px; text-align: center;">`;
  html += `<h2>Counter Islands</h2>`;
  html += `<div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap; margin: 20px 0;">`;
  html += `<div style="text-align: center;">`;
  html += `<h4>Immediate Hydration</h4>`;
  html += renderIsland('Counter', { initial: 5 });
  html += `</div>`;
  html += `<div style="text-align: center;">`;
  html += `<h4>Hydrate When Visible</h4>`;
  html += renderIsland('Counter', { initial: 10 }, 'visible');
  html += `</div>`;
  html += `<div style="text-align: center;">`;
  html += `<h4>Hydrate on Click</h4>`;
  html += renderIsland('Counter', { initial: 0 }, 'event');
  html += `</div>`;
  html += `</div>`;
  html += `</section>`;

  // Section 2: Interactive Components with Event Bus
  html += `<section style="margin-bottom: 40px;">`;
  html += `<h2 style="text-align: center;">Event Bus Communication</h2>`;
  html += `<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; max-width: 900px; margin: 0 auto;">`;
  html += `<div>`;
  html += renderIsland('Greeting', { name: 'ULRF User' });
  html += `</div>`;
  html += `<div>`;
  html += renderIsland('EventListener', { title: 'Message Receiver' });
  html += `</div>`;
  html += `</div>`;
  html += `<p style="text-align: center; margin-top: 20px; font-size: 14px; color: #666;">`;
  html += `Type in the greeting input and click "Broadcast Greeting" to see cross-component communication!`;
  html += `</p>`;
  html += `</section>`;

  // Section 3: Web Worker Demo
  html += `<section style="margin-bottom: 40px;">`;
  html += `<h2 style="text-align: center;">Web Worker Integration</h2>`;
  html += `<div style="max-width: 600px; margin: 0 auto;">`;
  html += renderIsland('ImageFilter', { title: 'Image Processing with Web Workers' }, 'visible');
  html += `</div>`;
  html += `<p style="text-align: center; margin-top: 20px; font-size: 14px; color: #666;">`;
  html += `Image processing runs in a separate thread to avoid blocking the main UI!`;
  html += `</p>`;
  html += `</section>`;

  html += `</main>`;

  // Section 4: Performance Monitor
  html += `<section style="margin-bottom: 40px;">`;
  html += `<h2 style="text-align: center;">Performance Monitoring</h2>`;
  html += `<div style="max-width: 400px; margin: 0 auto;">`;
  html += renderIsland('PerformanceMonitor', { title: 'Real-time Performance' });
  html += `</div>`;
  html += `</section>`;

  html += `<footer style="padding: 20px; background: #f0f0f0; text-align: center; margin-top: 50px;">`;
  html += `<p>© 2025 ULRF Demo - Built with Vite</p>`;
  html += `<p style="font-size: 12px; color: #888;">Client bundle size: ~2.3KB gzipped | SSR + Islands Architecture</p>`;
  html += `</footer>`;
  return html;
}
