// ----------------------------------
// src/client/hydrate.ts
// ----------------------------------
import { Counter } from '../components/Counter';
import { Greeting } from '../components/Greeting';
import { EventListener } from '../components/EventListener';
import { ImageFilter } from '../components/ImageFilter';
import { PerformanceMonitor } from '../components/PerformanceMonitor';

// A registry mapping componentName → hydration function
const registry: Record<string, (props: any) => HTMLElement> = {
  Counter: (props: { initial?: number }) => {
    return Counter(props);
  },
  Greeting: (props: { name?: string }) => {
    return Greeting(props);
  },
  EventListener: (props: { title?: string }) => {
    return EventListener(props);
  },
  ImageFilter: (props: { title?: string }) => {
    return ImageFilter(props);
  },
  PerformanceMonitor: (props: { title?: string }) => {
    return PerformanceMonitor(props);
  }
};

export function hydrate() {
  console.log('🏝️ ULRF: Starting hydration...');

  // Select all elements with a data-ulrf-component attribute
  const islands = document.querySelectorAll<HTMLElement>(
    '[data-ulrf-component]'
  );

  console.log(`🏝️ ULRF: Found ${islands.length} islands to hydrate`);

  islands.forEach((placeholder) => {
    const hydrateStrategy = placeholder.getAttribute('data-hydrate') || 'load';
    switch (hydrateStrategy) {
      case 'idle':
        // Wait for idle period
        if ('requestIdleCallback' in window) {
          (window as any).requestIdleCallback(() => doHydrate(placeholder));
        } else {
          setTimeout(() => doHydrate(placeholder), 200);
        }
        break;
      case 'visible':
        const observer = new IntersectionObserver(
          (entries, obs) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                obs.disconnect();
                doHydrate(placeholder);
              }
            });
          },
          { threshold: 0.1 }
        );
        observer.observe(placeholder);
        break;
      case 'event':
        // Wait for a click (or any event type you choose)
        const handler = () => {
          placeholder.removeEventListener('click', handler);
          doHydrate(placeholder);
        };
        placeholder.addEventListener('click', handler);
        break;
      default:
        // 'load': hydrate immediately
        doHydrate(placeholder);
        break;
    }
  });
}

function doHydrate(placeholder: HTMLElement) {
  const componentName = placeholder.getAttribute('data-ulrf-component');
  const propsJson = placeholder.getAttribute('data-props') || '{}';

  console.log(`🏝️ ULRF: Hydrating ${componentName} with props:`, propsJson);

  const props = JSON.parse(
    propsJson.replace(/&quot;/g, '"')
  );

  // Find the correct hydration function in the registry
  const hydrator = componentName ? registry[componentName] : null;
  if (!hydrator) {
    console.warn(`❌ ULRF: No component registered for ${componentName}`);
    return;
  }

  try {
    // Instantiate the actual component (this invokes its reactive core)
    const liveNode = hydrator(props);

    // Replace the placeholder in the DOM
    placeholder.replaceWith(liveNode);

    console.log(`✅ ULRF: Successfully hydrated ${componentName}`);
  } catch (error) {
    console.error(`❌ ULRF: Error hydrating ${componentName}:`, error);
  }
}
