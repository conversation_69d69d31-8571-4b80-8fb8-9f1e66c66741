// ----------------------------------
// src/client/component.ts
// Developer-friendly component abstractions
// ----------------------------------

import { createSignal, effect, eventBus } from './reactive';

export interface ComponentProps {
  [key: string]: any;
}

export interface ComponentState {
  [key: string]: any;
}

export interface ComponentMethods {
  emit: (eventName: string, data?: any) => void;
  listen: (eventName: string, handler: (data: any) => void) => void;
  cleanup: () => void;
}

export interface ComponentDefinition<P = ComponentProps, S = ComponentState> {
  props?: P;
  state?: () => S;
  template: (props: P, state: S, methods: ComponentMethods) => HTMLElement;
  mounted?: (element: HTMLElement, props: P, state: S, methods: ComponentMethods) => void;
  unmounted?: () => void;
}

// Helper to create reactive state
export function useState<T>(initialValue: T) {
  const [get, set] = createSignal(initialValue);
  return {
    get value() { return get(); },
    set value(newValue: T) { set(newValue); },
    get: get,
    set: set
  };
}

// Helper to create computed values
export function useComputed<T>(fn: () => T) {
  const [get, set] = createSignal(fn());
  effect(() => {
    set(fn());
  });
  return get;
}

// Helper to create effects with cleanup
export function useEffect(fn: () => void | (() => void), deps?: (() => any)[]) {
  let cleanup: (() => void) | void;
  
  const runEffect = () => {
    if (cleanup) cleanup();
    cleanup = fn();
  };
  
  if (deps && deps.length > 0) {
    // Watch dependencies
    effect(() => {
      deps.forEach(dep => dep()); // Read all deps to subscribe
      runEffect();
    });
  } else {
    // Run immediately
    effect(runEffect);
  }
  
  return () => {
    if (cleanup) cleanup();
  };
}

// Helper to create event listeners
export function useEventBus() {
  const listeners: Array<{ event: string; handler: EventListener }> = [];
  
  const emit = (eventName: string, data?: any) => {
    eventBus.dispatchEvent(new CustomEvent(eventName, { detail: data }));
  };
  
  const listen = (eventName: string, handler: (data: any) => void) => {
    const eventHandler = (e: Event) => handler((e as CustomEvent).detail);
    eventBus.addEventListener(eventName, eventHandler);
    listeners.push({ event: eventName, handler: eventHandler });
  };
  
  const cleanup = () => {
    listeners.forEach(({ event, handler }) => {
      eventBus.removeEventListener(event, handler);
    });
    listeners.length = 0;
  };
  
  return { emit, listen, cleanup };
}

// Main component factory
export function defineComponent<P = ComponentProps, S = ComponentState>(
  definition: ComponentDefinition<P, S>
) {
  return function createComponent(props: P): HTMLElement {
    // Initialize state
    const state = definition.state ? definition.state() : {} as S;
    
    // Create event bus methods
    const eventMethods = useEventBus();
    
    const methods: ComponentMethods = {
      emit: eventMethods.emit,
      listen: eventMethods.listen,
      cleanup: eventMethods.cleanup
    };
    
    // Create the template
    const element = definition.template(props, state, methods);
    
    // Call mounted lifecycle
    if (definition.mounted) {
      definition.mounted(element, props, state, methods);
    }
    
    // Store cleanup for potential future use
    (element as any).__ulrfCleanup = () => {
      methods.cleanup();
      if (definition.unmounted) {
        definition.unmounted();
      }
    };
    
    return element;
  };
}

// Helper to create DOM elements with reactive content
export function createElement(
  tag: string, 
  attributes: Record<string, string | (() => string)> = {},
  children: (HTMLElement | string | (() => string))[] = []
): HTMLElement {
  const element = document.createElement(tag);
  
  // Set attributes (reactive or static)
  Object.entries(attributes).forEach(([key, value]) => {
    if (typeof value === 'function') {
      effect(() => {
        element.setAttribute(key, value());
      });
    } else {
      element.setAttribute(key, value);
    }
  });
  
  // Add children (reactive or static)
  children.forEach(child => {
    if (typeof child === 'string') {
      element.appendChild(document.createTextNode(child));
    } else if (typeof child === 'function') {
      const textNode = document.createTextNode('');
      element.appendChild(textNode);
      effect(() => {
        textNode.textContent = child();
      });
    } else {
      element.appendChild(child);
    }
  });
  
  return element;
}

// Helper to create styled elements
export function styled(
  tag: string,
  styles: Record<string, string | (() => string)>
) {
  return (
    attributes: Record<string, string | (() => string)> = {},
    children: (HTMLElement | string | (() => string))[] = []
  ) => {
    const element = createElement(tag, attributes, children);
    
    // Apply styles (reactive or static)
    Object.entries(styles).forEach(([property, value]) => {
      if (typeof value === 'function') {
        effect(() => {
          (element.style as any)[property] = value();
        });
      } else {
        (element.style as any)[property] = value;
      }
    });
    
    return element;
  };
}
