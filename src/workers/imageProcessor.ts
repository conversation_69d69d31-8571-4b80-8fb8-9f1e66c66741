// ----------------------------------
// src/workers/imageProcessor.ts
// ----------------------------------
// This code runs in a separate thread

self.onmessage = async (e) => {
  const { imageData, width, height, filter } = e.data;
  
  let processed: ImageData;
  
  switch (filter) {
    case 'grayscale':
      processed = applyGrayscale(imageData);
      break;
    case 'invert':
      processed = applyInvert(imageData);
      break;
    case 'blur':
      processed = applyBlur(imageData, width, height);
      break;
    default:
      processed = imageData;
  }
  
  // Post the processed image data back to main thread
  self.postMessage({ processed });
};

function applyGrayscale(imageData: ImageData): ImageData {
  const data = new Uint8ClampedArray(imageData.data);
  
  for (let i = 0; i < data.length; i += 4) {
    const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
    data[i] = avg;     // Red
    data[i + 1] = avg; // Green
    data[i + 2] = avg; // Blue
    // Alpha channel (i + 3) remains unchanged
  }
  
  return new ImageData(data, imageData.width, imageData.height);
}

function applyInvert(imageData: ImageData): ImageData {
  const data = new Uint8ClampedArray(imageData.data);
  
  for (let i = 0; i < data.length; i += 4) {
    data[i] = 255 - data[i];         // Red
    data[i + 1] = 255 - data[i + 1]; // Green
    data[i + 2] = 255 - data[i + 2]; // Blue
    // Alpha channel (i + 3) remains unchanged
  }
  
  return new ImageData(data, imageData.width, imageData.height);
}

function applyBlur(imageData: ImageData, width: number, height: number): ImageData {
  const data = new Uint8ClampedArray(imageData.data);
  const original = new Uint8ClampedArray(imageData.data);
  
  // Simple box blur
  const radius = 2;
  
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      let r = 0, g = 0, b = 0, count = 0;
      
      for (let dy = -radius; dy <= radius; dy++) {
        for (let dx = -radius; dx <= radius; dx++) {
          const ny = y + dy;
          const nx = x + dx;
          
          if (ny >= 0 && ny < height && nx >= 0 && nx < width) {
            const idx = (ny * width + nx) * 4;
            r += original[idx];
            g += original[idx + 1];
            b += original[idx + 2];
            count++;
          }
        }
      }
      
      const idx = (y * width + x) * 4;
      data[idx] = r / count;
      data[idx + 1] = g / count;
      data[idx + 2] = b / count;
    }
  }
  
  return new ImageData(data, imageData.width, imageData.height);
}
