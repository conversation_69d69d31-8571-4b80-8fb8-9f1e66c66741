// ----------------------------------
// src/components/ImageFilter.tsx
// ----------------------------------

import { createSignal, effect } from '../client/reactive';

interface ImageFilterProps {
  title?: string;
}

export function ImageFilter(props: ImageFilterProps) {
  const [isProcessing, setIsProcessing] = createSignal(false);
  const [currentFilter, setCurrentFilter] = createSignal('none');

  // Create container
  const container = document.createElement('div');
  container.style.padding = '20px';
  container.style.border = '1px solid #ddd';
  container.style.borderRadius = '8px';
  container.style.backgroundColor = '#fafafa';
  container.style.maxWidth = '500px';
  container.style.margin = '0 auto';

  // Title
  const title = document.createElement('h3');
  title.textContent = props.title || 'Image Filter (Web Worker Demo)';
  title.style.marginTop = '0';
  title.style.color = '#333';

  // Canvas for image display
  const canvas = document.createElement('canvas');
  canvas.width = 200;
  canvas.height = 150;
  canvas.style.border = '1px solid #ccc';
  canvas.style.display = 'block';
  canvas.style.margin = '10px auto';
  const ctx = canvas.getContext('2d')!;

  // Draw a simple test pattern
  drawTestPattern(ctx, canvas.width, canvas.height);

  // Filter buttons container
  const buttonsContainer = document.createElement('div');
  buttonsContainer.style.display = 'flex';
  buttonsContainer.style.gap = '10px';
  buttonsContainer.style.justifyContent = 'center';
  buttonsContainer.style.flexWrap = 'wrap';
  buttonsContainer.style.marginTop = '15px';

  // Status display
  const status = document.createElement('p');
  status.style.textAlign = 'center';
  status.style.fontSize = '14px';
  status.style.color = '#666';
  status.style.marginTop = '10px';

  // Create filter buttons
  const filters = [
    { name: 'Original', value: 'none' },
    { name: 'Grayscale', value: 'grayscale' },
    { name: 'Invert', value: 'invert' },
    { name: 'Blur', value: 'blur' }
  ];

  filters.forEach(filter => {
    const button = document.createElement('button');
    button.textContent = filter.name;
    button.style.padding = '8px 16px';
    button.style.fontSize = '14px';
    button.style.border = '1px solid #007acc';
    button.style.borderRadius = '4px';
    button.style.cursor = 'pointer';
    button.style.backgroundColor = 'white';
    button.style.color = '#007acc';

    button.addEventListener('click', () => {
      if (!isProcessing()) {
        applyFilter(filter.value);
      }
    });

    buttonsContainer.appendChild(button);
  });

  // Reactive effects
  effect(() => {
    if (isProcessing()) {
      status.textContent = `Processing ${currentFilter()}...`;
    } else {
      status.textContent = `Current filter: ${currentFilter()}`;
    }
  });

  async function applyFilter(filterType: string) {
    if (filterType === 'none') {
      drawTestPattern(ctx, canvas.width, canvas.height);
      setCurrentFilter('none');
      return;
    }

    setIsProcessing(true);
    setCurrentFilter(filterType);

    try {
      // Get current image data
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

      // Create and use Web Worker
      const worker = new Worker(
        new URL('../workers/imageProcessor.ts', import.meta.url),
        { type: 'module' }
      );

      worker.postMessage({
        imageData,
        width: canvas.width,
        height: canvas.height,
        filter: filterType
      });

      worker.onmessage = (e) => {
        const { processed } = e.data;
        ctx.putImageData(processed, 0, 0);
        worker.terminate();
        setIsProcessing(false);
      };

      worker.onerror = (error) => {
        console.error('Worker error:', error);
        worker.terminate();
        setIsProcessing(false);
      };

    } catch (error) {
      console.error('Filter error:', error);
      setIsProcessing(false);
    }
  }

  // Assemble component
  container.appendChild(title);
  container.appendChild(canvas);
  container.appendChild(buttonsContainer);
  container.appendChild(status);

  return container;
}

function drawTestPattern(ctx: CanvasRenderingContext2D, width: number, height: number) {
  // Create a colorful test pattern
  const gradient = ctx.createLinearGradient(0, 0, width, height);
  gradient.addColorStop(0, '#ff6b6b');
  gradient.addColorStop(0.25, '#4ecdc4');
  gradient.addColorStop(0.5, '#45b7d1');
  gradient.addColorStop(0.75, '#96ceb4');
  gradient.addColorStop(1, '#ffeaa7');

  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, width, height);

  // Add some shapes
  ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
  ctx.fillRect(20, 20, 60, 40);
  
  ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
  ctx.beginPath();
  ctx.arc(150, 75, 30, 0, Math.PI * 2);
  ctx.fill();

  // Add text
  ctx.fillStyle = '#333';
  ctx.font = '16px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('Test Image', width / 2, height - 20);
}
