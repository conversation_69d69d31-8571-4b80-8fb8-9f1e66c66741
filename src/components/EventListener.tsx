// ----------------------------------
// src/components/EventListener.tsx
// ----------------------------------

import { createSignal, effect, eventBus } from '../client/reactive';

interface EventListenerProps {
  title?: string;
}

// EventListener component that listens for broadcast events
export function EventListener(props: EventListenerProps) {
  const [lastMessage, setLastMessage] = createSignal('No messages yet...');
  const [messageCount, setMessageCount] = createSignal(0);

  // Create container div
  const container = document.createElement('div');
  container.style.padding = '20px';
  container.style.border = '2px solid #007acc';
  container.style.borderRadius = '8px';
  container.style.backgroundColor = '#e8f4fd';
  container.style.maxWidth = '400px';
  container.style.margin = '0 auto';

  // Create title
  const title = document.createElement('h3');
  title.textContent = props.title || 'Event Listener';
  title.style.marginTop = '0';
  title.style.color = '#007acc';

  // Create message display
  const messageDisplay = document.createElement('div');
  messageDisplay.style.padding = '10px';
  messageDisplay.style.backgroundColor = 'white';
  messageDisplay.style.border = '1px solid #ddd';
  messageDisplay.style.borderRadius = '4px';
  messageDisplay.style.marginBottom = '10px';
  messageDisplay.style.minHeight = '40px';
  messageDisplay.style.fontSize = '16px';

  // Create counter display
  const counterDisplay = document.createElement('p');
  counterDisplay.style.fontSize = '14px';
  counterDisplay.style.color = '#666';
  counterDisplay.style.margin = '0';

  // Listen for greeting broadcast events
  eventBus.addEventListener('greetingBroadcast', (e) => {
    const event = e as CustomEvent;
    setLastMessage(event.detail.message);
    setMessageCount(messageCount() + 1);
  });

  // Reactive effects
  effect(() => {
    messageDisplay.textContent = lastMessage();
  });

  effect(() => {
    counterDisplay.textContent = `Messages received: ${messageCount()}`;
  });

  // Assemble the component
  container.appendChild(title);
  container.appendChild(messageDisplay);
  container.appendChild(counterDisplay);

  return container;
}
