// ----------------------------------
// src/components/SimpleCounter.tsx
// Example using the new developer-friendly API
// ----------------------------------

import { defineComponent, useState, createElement, styled } from '../client/component';

interface SimpleCounterProps {
  initial?: number;
  label?: string;
}

// Create styled components
const CounterButton = styled('button', {
  padding: '12px 24px',
  fontSize: '16px',
  fontWeight: 'bold',
  border: 'none',
  borderRadius: '8px',
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  backgroundColor: '#007acc',
  color: 'white'
});

const CounterContainer = styled('div', {
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: '10px',
  padding: '20px',
  border: '1px solid #ddd',
  borderRadius: '8px',
  backgroundColor: '#f9f9f9',
  maxWidth: '200px',
  margin: '0 auto'
});

export const SimpleCounter = defineComponent<SimpleCounterProps>({
  template: (props, state, methods) => {
    // Create reactive state
    const count = useState(props.initial ?? 0);
    
    // Create container
    const container = CounterContainer();
    
    // Create label
    const label = createElement('h4', {
      style: 'margin: 0 0 10px 0; color: #333;'
    }, [props.label || 'Simple Counter']);
    
    // Create display
    const display = createElement('div', {
      style: 'font-size: 24px; font-weight: bold; color: #007acc; margin: 10px 0;'
    }, [() => `Count: ${count.value}`]);
    
    // Create buttons container
    const buttonsContainer = createElement('div', {
      style: 'display: flex; gap: 10px;'
    });
    
    // Create increment button
    const incrementBtn = CounterButton({}, [() => `+1 (${count.value})`]);
    incrementBtn.addEventListener('click', () => {
      count.value++;
      methods.emit('counterChanged', { value: count.value, action: 'increment' });
    });
    
    // Create decrement button
    const decrementBtn = CounterButton({
      style: 'background-color: #dc3545;'
    }, [() => `−1 (${count.value})`]);
    decrementBtn.addEventListener('click', () => {
      count.value--;
      methods.emit('counterChanged', { value: count.value, action: 'decrement' });
    });
    
    // Create reset button
    const resetBtn = CounterButton({
      style: 'background-color: #28a745;'
    }, ['Reset']);
    resetBtn.addEventListener('click', () => {
      count.value = props.initial ?? 0;
      methods.emit('counterChanged', { value: count.value, action: 'reset' });
    });
    
    // Assemble buttons
    buttonsContainer.appendChild(incrementBtn);
    buttonsContainer.appendChild(decrementBtn);
    buttonsContainer.appendChild(resetBtn);
    
    // Assemble container
    container.appendChild(label);
    container.appendChild(display);
    container.appendChild(buttonsContainer);
    
    return container;
  },
  
  mounted: (element, props, state, methods) => {
    console.log(`✅ SimpleCounter mounted with initial value: ${props.initial}`);
    
    // Listen for external events
    methods.listen('resetAllCounters', () => {
      // This would reset the counter if another component broadcasts this event
      console.log('Received reset all counters event');
    });
  },
  
  unmounted: () => {
    console.log('SimpleCounter unmounted');
  }
});
