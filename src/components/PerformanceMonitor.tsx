// ----------------------------------
// src/components/PerformanceMonitor.tsx
// ----------------------------------

import { createSignal, effect } from '../client/reactive';

interface PerformanceMonitorProps {
  title?: string;
}

export function PerformanceMonitor(props: PerformanceMonitorProps) {
  const [fps, setFps] = createSignal(0);
  const [memoryUsage, setMemoryUsage] = createSignal(0);
  const [updateCount, setUpdateCount] = createSignal(0);

  // Create container
  const container = document.createElement('div');
  container.style.padding = '15px';
  container.style.border = '1px solid #28a745';
  container.style.borderRadius = '8px';
  container.style.backgroundColor = '#f8fff9';
  container.style.maxWidth = '300px';
  container.style.margin = '0 auto';
  container.style.fontSize = '14px';

  // Title
  const title = document.createElement('h4');
  title.textContent = props.title || 'Performance Monitor';
  title.style.marginTop = '0';
  title.style.marginBottom = '15px';
  title.style.color = '#28a745';
  title.style.textAlign = 'center';

  // Metrics container
  const metricsContainer = document.createElement('div');
  metricsContainer.style.display = 'grid';
  metricsContainer.style.gridTemplateColumns = '1fr 1fr';
  metricsContainer.style.gap = '10px';

  // FPS display
  const fpsContainer = document.createElement('div');
  const fpsLabel = document.createElement('div');
  fpsLabel.textContent = 'FPS:';
  fpsLabel.style.fontWeight = 'bold';
  fpsLabel.style.color = '#666';
  const fpsValue = document.createElement('div');
  fpsValue.style.fontSize = '18px';
  fpsValue.style.color = '#28a745';
  fpsValue.style.fontWeight = 'bold';
  fpsContainer.appendChild(fpsLabel);
  fpsContainer.appendChild(fpsValue);

  // Memory display
  const memoryContainer = document.createElement('div');
  const memoryLabel = document.createElement('div');
  memoryLabel.textContent = 'Memory:';
  memoryLabel.style.fontWeight = 'bold';
  memoryLabel.style.color = '#666';
  const memoryValue = document.createElement('div');
  memoryValue.style.fontSize = '18px';
  memoryValue.style.color = '#007acc';
  memoryValue.style.fontWeight = 'bold';
  memoryContainer.appendChild(memoryLabel);
  memoryContainer.appendChild(memoryValue);

  // Update counter
  const updateContainer = document.createElement('div');
  updateContainer.style.gridColumn = '1 / -1';
  updateContainer.style.textAlign = 'center';
  updateContainer.style.marginTop = '10px';
  const updateLabel = document.createElement('div');
  updateLabel.textContent = 'Reactive Updates:';
  updateLabel.style.fontWeight = 'bold';
  updateLabel.style.color = '#666';
  const updateValue = document.createElement('div');
  updateValue.style.fontSize = '16px';
  updateValue.style.color = '#dc3545';
  updateValue.style.fontWeight = 'bold';
  updateContainer.appendChild(updateLabel);
  updateContainer.appendChild(updateValue);

  // Performance info
  const infoContainer = document.createElement('div');
  infoContainer.style.gridColumn = '1 / -1';
  infoContainer.style.marginTop = '15px';
  infoContainer.style.padding = '10px';
  infoContainer.style.backgroundColor = '#e9ecef';
  infoContainer.style.borderRadius = '4px';
  infoContainer.style.fontSize = '12px';
  infoContainer.style.color = '#495057';
  infoContainer.innerHTML = `
    <strong>Bundle Size:</strong> ~2.3KB gzipped<br>
    <strong>Runtime:</strong> Minimal overhead<br>
    <strong>Hydration:</strong> Selective islands
  `;

  // Assemble metrics
  metricsContainer.appendChild(fpsContainer);
  metricsContainer.appendChild(memoryContainer);
  metricsContainer.appendChild(updateContainer);
  metricsContainer.appendChild(infoContainer);

  // Assemble component
  container.appendChild(title);
  container.appendChild(metricsContainer);

  // FPS monitoring
  let frameCount = 0;
  let lastTime = performance.now();
  
  function measureFPS() {
    frameCount++;
    const currentTime = performance.now();
    
    if (currentTime - lastTime >= 1000) {
      setFps(Math.round(frameCount * 1000 / (currentTime - lastTime)));
      frameCount = 0;
      lastTime = currentTime;
    }
    
    requestAnimationFrame(measureFPS);
  }
  
  measureFPS();

  // Memory monitoring (if available)
  function updateMemory() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
      setMemoryUsage(usedMB);
    }
  }

  setInterval(updateMemory, 1000);

  // Reactive effects (these will increment the update counter)
  effect(() => {
    fpsValue.textContent = `${fps()}`;
    setUpdateCount(updateCount() + 1);
  });

  effect(() => {
    if (memoryUsage() > 0) {
      memoryValue.textContent = `${memoryUsage()}MB`;
    } else {
      memoryValue.textContent = 'N/A';
    }
    setUpdateCount(updateCount() + 1);
  });

  effect(() => {
    updateValue.textContent = `${updateCount()}`;
  });

  return container;
}
