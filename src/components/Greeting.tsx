// ----------------------------------
// src/components/Greeting.tsx
// ----------------------------------

import { createSignal, effect, eventBus } from '../client/reactive';

interface GreetingProps {
  name?: string;
}

// Greeting component with input field and reactive updates
export function Greeting(props: GreetingProps) {
  const [name, setName] = createSignal(props.name ?? 'World');

  // Create container div
  const container = document.createElement('div');
  container.style.padding = '20px';
  container.style.border = '1px solid #ccc';
  container.style.borderRadius = '8px';
  container.style.backgroundColor = '#f9f9f9';
  container.style.maxWidth = '400px';
  container.style.margin = '0 auto';

  // Create title
  const title = document.createElement('h3');
  title.textContent = 'Interactive Greeting';
  title.style.marginTop = '0';
  title.style.color = '#333';

  // Create input field
  const input = document.createElement('input');
  input.type = 'text';
  input.value = name();
  input.placeholder = 'Enter your name';
  input.style.padding = '8px 12px';
  input.style.fontSize = '14px';
  input.style.border = '1px solid #ddd';
  input.style.borderRadius = '4px';
  input.style.width = '200px';
  input.style.marginRight = '10px';

  // Create greeting display
  const greeting = document.createElement('p');
  greeting.style.fontSize = '18px';
  greeting.style.fontWeight = 'bold';
  greeting.style.color = '#007acc';
  greeting.style.marginTop = '15px';

  // Create button to broadcast event
  const broadcastBtn = document.createElement('button');
  broadcastBtn.textContent = 'Broadcast Greeting';
  broadcastBtn.style.padding = '8px 16px';
  broadcastBtn.style.fontSize = '14px';
  broadcastBtn.style.backgroundColor = '#28a745';
  broadcastBtn.style.color = 'white';
  broadcastBtn.style.border = 'none';
  broadcastBtn.style.borderRadius = '4px';
  broadcastBtn.style.cursor = 'pointer';
  broadcastBtn.style.marginTop = '10px';

  // Event listeners
  input.addEventListener('input', (e) => {
    const target = e.target as HTMLInputElement;
    setName(target.value || 'World');
  });

  broadcastBtn.addEventListener('click', () => {
    eventBus.dispatchEvent(new CustomEvent('greetingBroadcast', { 
      detail: { name: name(), message: `Hello, ${name()}!` } 
    }));
  });

  // Reactive effect: update greeting when name changes
  effect(() => {
    greeting.textContent = `Hello, ${name()}! 👋`;
  });

  // Assemble the component
  container.appendChild(title);
  container.appendChild(input);
  container.appendChild(greeting);
  container.appendChild(broadcastBtn);

  return container;
}
