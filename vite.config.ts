import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  // 1. Plugins
  plugins: [react()],

  // 2. Path aliases
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    }
  },

  // 3. Build configuration
  build: {
    outDir: 'dist/client',
    rollupOptions: {
      input: {
        client: resolve(__dirname, 'src/client/entry-client.ts')
      }
    }
  },

  // 4. SSR configuration
  ssr: {
    // Tell Vite not to externalize the React plugin
    noExternal: ['@vitejs/plugin-react']
  }
});
