let W=new Set,M=!1;function I(t){W.add(t),M||(M=!0,requestAnimationFrame(()=>{M=!1;const r=Array.from(W);W.clear(),r.forEach(s=>s())}))}let k=null;function C(t){let r=t;const s=new Set;return[()=>(k&&s.add(k),r),e=>{e!==r&&(r=e,s.forEach(a=>I(a)))}]}function f(t){k=t,t(),k=null}const v=new EventTarget;function P(t){const[r,s]=C(t.initial??0),n=document.createElement("button");return n.textContent=String(r()),n.style.padding="10px 20px",n.style.fontSize="16px",n.style.backgroundColor="#007acc",n.style.color="white",n.style.border="none",n.style.borderRadius="4px",n.style.cursor="pointer",n.addEventListener("click",()=>s(r()+1)),f(()=>{n.textContent=`Count: ${r()}`}),n}function U(t){const[r,s]=C(t.name??"World"),n=document.createElement("div");n.style.padding="20px",n.style.border="1px solid #ccc",n.style.borderRadius="8px",n.style.backgroundColor="#f9f9f9",n.style.maxWidth="400px",n.style.margin="0 auto";const l=document.createElement("h3");l.textContent="Interactive Greeting",l.style.marginTop="0",l.style.color="#333";const e=document.createElement("input");e.type="text",e.value=r(),e.placeholder="Enter your name",e.style.padding="8px 12px",e.style.fontSize="14px",e.style.border="1px solid #ddd",e.style.borderRadius="4px",e.style.width="200px",e.style.marginRight="10px";const a=document.createElement("p");a.style.fontSize="18px",a.style.fontWeight="bold",a.style.color="#007acc",a.style.marginTop="15px";const o=document.createElement("button");return o.textContent="Broadcast Greeting",o.style.padding="8px 16px",o.style.fontSize="14px",o.style.backgroundColor="#28a745",o.style.color="white",o.style.border="none",o.style.borderRadius="4px",o.style.cursor="pointer",o.style.marginTop="10px",e.addEventListener("input",c=>{const d=c.target;s(d.value||"World")}),o.addEventListener("click",()=>{v.dispatchEvent(new CustomEvent("greetingBroadcast",{detail:{name:r(),message:`Hello, ${r()}!`}}))}),f(()=>{a.textContent=`Hello, ${r()}! 👋`}),n.appendChild(l),n.appendChild(e),n.appendChild(a),n.appendChild(o),n}function H(t){const[r,s]=C("No messages yet..."),[n,l]=C(0),e=document.createElement("div");e.style.padding="20px",e.style.border="2px solid #007acc",e.style.borderRadius="8px",e.style.backgroundColor="#e8f4fd",e.style.maxWidth="400px",e.style.margin="0 auto";const a=document.createElement("h3");a.textContent=t.title||"Event Listener",a.style.marginTop="0",a.style.color="#007acc";const o=document.createElement("div");o.style.padding="10px",o.style.backgroundColor="white",o.style.border="1px solid #ddd",o.style.borderRadius="4px",o.style.marginBottom="10px",o.style.minHeight="40px",o.style.fontSize="16px";const c=document.createElement("p");return c.style.fontSize="14px",c.style.color="#666",c.style.margin="0",v.addEventListener("greetingBroadcast",d=>{s(d.detail.message),l(n()+1)}),f(()=>{o.textContent=r()}),f(()=>{c.textContent=`Messages received: ${n()}`}),e.appendChild(a),e.appendChild(o),e.appendChild(c),e}function N(t){const[r,s]=C(!1),[n,l]=C("none"),e=document.createElement("div");e.style.padding="20px",e.style.border="1px solid #ddd",e.style.borderRadius="8px",e.style.backgroundColor="#fafafa",e.style.maxWidth="500px",e.style.margin="0 auto";const a=document.createElement("h3");a.textContent=t.title||"Image Filter (Web Worker Demo)",a.style.marginTop="0",a.style.color="#333";const o=document.createElement("canvas");o.width=200,o.height=150,o.style.border="1px solid #ccc",o.style.display="block",o.style.margin="10px auto";const c=o.getContext("2d");$(c,o.width,o.height);const d=document.createElement("div");d.style.display="flex",d.style.gap="10px",d.style.justifyContent="center",d.style.flexWrap="wrap",d.style.marginTop="15px";const u=document.createElement("p");u.style.textAlign="center",u.style.fontSize="14px",u.style.color="#666",u.style.marginTop="10px",[{name:"Original",value:"none"},{name:"Grayscale",value:"grayscale"},{name:"Invert",value:"invert"},{name:"Blur",value:"blur"}].forEach(p=>{const i=document.createElement("button");i.textContent=p.name,i.style.padding="8px 16px",i.style.fontSize="14px",i.style.border="1px solid #007acc",i.style.borderRadius="4px",i.style.cursor="pointer",i.style.backgroundColor="white",i.style.color="#007acc",i.addEventListener("click",()=>{r()||x(p.value)}),d.appendChild(i)}),f(()=>{r()?u.textContent=`Processing ${n()}...`:u.textContent=`Current filter: ${n()}`});async function x(p){if(p==="none"){$(c,o.width,o.height),l("none");return}s(!0),l(p);try{const i=c.getImageData(0,0,o.width,o.height),y=new Worker(new URL("/assets/imageProcessor-4d7f28aa.js",self.location),{type:"module"});y.postMessage({imageData:i,width:o.width,height:o.height,filter:p}),y.onmessage=g=>{const{processed:m}=g.data;c.putImageData(m,0,0),y.terminate(),s(!1)},y.onerror=g=>{console.error("Worker error:",g),y.terminate(),s(!1)}}catch(i){console.error("Filter error:",i),s(!1)}}return e.appendChild(a),e.appendChild(o),e.appendChild(d),e.appendChild(u),e}function $(t,r,s){const n=t.createLinearGradient(0,0,r,s);n.addColorStop(0,"#ff6b6b"),n.addColorStop(.25,"#4ecdc4"),n.addColorStop(.5,"#45b7d1"),n.addColorStop(.75,"#96ceb4"),n.addColorStop(1,"#ffeaa7"),t.fillStyle=n,t.fillRect(0,0,r,s),t.fillStyle="rgba(255, 255, 255, 0.8)",t.fillRect(20,20,60,40),t.fillStyle="rgba(0, 0, 0, 0.6)",t.beginPath(),t.arc(150,75,30,0,Math.PI*2),t.fill(),t.fillStyle="#333",t.font="16px Arial",t.textAlign="center",t.fillText("Test Image",r/2,s-20)}function D(t){const[r,s]=C(0),[n,l]=C(0),e=document.createElement("div");e.style.padding="15px",e.style.border="1px solid #28a745",e.style.borderRadius="8px",e.style.backgroundColor="#f8fff9",e.style.maxWidth="300px",e.style.margin="0 auto",e.style.fontSize="14px";const a=document.createElement("h4");a.textContent=t.title||"Performance Monitor",a.style.marginTop="0",a.style.marginBottom="15px",a.style.color="#28a745",a.style.textAlign="center";const o=document.createElement("div");o.style.display="grid",o.style.gridTemplateColumns="1fr 1fr",o.style.gap="10px";const c=document.createElement("div"),d=document.createElement("div");d.textContent="FPS:",d.style.fontWeight="bold",d.style.color="#666";const u=document.createElement("div");u.style.fontSize="18px",u.style.color="#28a745",u.style.fontWeight="bold",c.appendChild(d),c.appendChild(u);const E=document.createElement("div"),x=document.createElement("div");x.textContent="Memory:",x.style.fontWeight="bold",x.style.color="#666";const p=document.createElement("div");p.style.fontSize="18px",p.style.color="#007acc",p.style.fontWeight="bold",E.appendChild(x),E.appendChild(p);const i=document.createElement("div");i.style.gridColumn="1 / -1",i.style.textAlign="center",i.style.marginTop="10px";const y=document.createElement("div");y.textContent="Reactive Updates:",y.style.fontWeight="bold",y.style.color="#666";const g=document.createElement("div");g.style.fontSize="16px",g.style.color="#dc3545",g.style.fontWeight="bold",i.appendChild(y),i.appendChild(g);const m=document.createElement("div");m.style.gridColumn="1 / -1",m.style.marginTop="15px",m.style.padding="10px",m.style.backgroundColor="#e9ecef",m.style.borderRadius="4px",m.style.fontSize="12px",m.style.color="#495057",m.innerHTML=`
    <strong>Bundle Size:</strong> ~2.3KB gzipped<br>
    <strong>Runtime:</strong> Minimal overhead<br>
    <strong>Hydration:</strong> Selective islands
  `,o.appendChild(c),o.appendChild(E),o.appendChild(i),o.appendChild(m),e.appendChild(a),e.appendChild(o);let L=0,R=performance.now();function T(){L++;const h=performance.now();h-R>=1e3&&(s(Math.round(L*1e3/(h-R))),L=0,R=h),requestAnimationFrame(T)}T();function B(){if("memory"in performance){const h=performance.memory,A=Math.round(h.usedJSHeapSize/1024/1024);l(A)}}setInterval(B,1e3);let S=0;return f(()=>{u.textContent=`${r()}`,S++,g.textContent=`${S}`}),f(()=>{n()>0?p.textContent=`${n()}MB`:p.textContent="N/A",S++,g.textContent=`${S}`}),e}function q(t){const[r,s]=C(t);return{get value(){return r()},set value(n){s(n)},get:r,set:s}}function G(){const t=[];return{emit:(l,e)=>{v.dispatchEvent(new CustomEvent(l,{detail:e}))},listen:(l,e)=>{const a=o=>e(o.detail);v.addEventListener(l,a),t.push({event:l,handler:a})},cleanup:()=>{t.forEach(({event:l,handler:e})=>{v.removeEventListener(l,e)}),t.length=0}}}function O(t){return function(s){const n=t.state?t.state():{},l=G(),e={emit:l.emit,listen:l.listen,cleanup:l.cleanup},a=t.template(s,n,e);return t.mounted&&t.mounted(a,s,n,e),a.__ulrfCleanup=()=>{e.cleanup(),t.unmounted&&t.unmounted()},a}}function w(t,r={},s=[]){const n=document.createElement(t);return Object.entries(r).forEach(([l,e])=>{typeof e=="function"?f(()=>{n.setAttribute(l,e())}):n.setAttribute(l,e)}),s.forEach(l=>{if(typeof l=="string")n.appendChild(document.createTextNode(l));else if(typeof l=="function"){const e=document.createTextNode("");n.appendChild(e),f(()=>{e.textContent=l()})}else n.appendChild(l)}),n}function z(t,r){return(s={},n=[])=>{const l=w(t,s,n);return Object.entries(r).forEach(([e,a])=>{typeof a=="function"?f(()=>{l.style[e]=a()}):l.style[e]=a}),l}}const F=z("button",{padding:"12px 24px",fontSize:"16px",fontWeight:"bold",border:"none",borderRadius:"8px",cursor:"pointer",transition:"all 0.2s ease",backgroundColor:"#007acc",color:"white"}),j=z("div",{display:"flex",flexDirection:"column",alignItems:"center",gap:"10px",padding:"20px",border:"1px solid #ddd",borderRadius:"8px",backgroundColor:"#f9f9f9",maxWidth:"200px",margin:"0 auto"}),J=O({template:(t,r,s)=>{const n=q(t.initial??0),l=j(),e=w("h4",{style:"margin: 0 0 10px 0; color: #333;"},[t.label||"Simple Counter"]),a=w("div",{style:"font-size: 24px; font-weight: bold; color: #007acc; margin: 10px 0;"},[()=>`Count: ${n.value}`]),o=w("div",{style:"display: flex; gap: 10px;"}),c=F({},[()=>`+1 (${n.value})`]);c.addEventListener("click",()=>{n.value++,s.emit("counterChanged",{value:n.value,action:"increment"})});const d=F({style:"background-color: #dc3545;"},[()=>`−1 (${n.value})`]);d.addEventListener("click",()=>{n.value--,s.emit("counterChanged",{value:n.value,action:"decrement"})});const u=F({style:"background-color: #28a745;"},["Reset"]);return u.addEventListener("click",()=>{n.value=t.initial??0,s.emit("counterChanged",{value:n.value,action:"reset"})}),o.appendChild(c),o.appendChild(d),o.appendChild(u),l.appendChild(e),l.appendChild(a),l.appendChild(o),l},mounted:(t,r,s,n)=>{console.log(`✅ SimpleCounter mounted with initial value: ${r.initial}`),n.listen("resetAllCounters",()=>{console.log("Received reset all counters event")})},unmounted:()=>{console.log("SimpleCounter unmounted")}}),V={Counter:t=>P(t),SimpleCounter:t=>J(t),Greeting:t=>U(t),EventListener:t=>H(t),ImageFilter:t=>N(t),PerformanceMonitor:t=>D(t)};function _(){console.log("🏝️ ULRF: Starting hydration...");const t=document.querySelectorAll("[data-ulrf-component]");console.log(`🏝️ ULRF: Found ${t.length} islands to hydrate`),t.forEach(r=>{switch(r.getAttribute("data-hydrate")||"load"){case"idle":"requestIdleCallback"in window?window.requestIdleCallback(()=>b(r)):setTimeout(()=>b(r),200);break;case"visible":new IntersectionObserver((e,a)=>{e.forEach(o=>{o.isIntersecting&&(a.disconnect(),b(r))})},{threshold:.1}).observe(r);break;case"event":const l=()=>{r.removeEventListener("click",l),b(r)};r.addEventListener("click",l);break;default:b(r);break}})}function b(t){const r=t.getAttribute("data-ulrf-component"),s=t.getAttribute("data-props")||"{}";console.log(`🏝️ ULRF: Hydrating ${r} with props:`,s);const n=JSON.parse(s.replace(/&quot;/g,'"')),l=r?V[r]:null;if(!l){console.warn(`❌ ULRF: No component registered for ${r}`);return}try{const e=l(n);t.replaceWith(e),console.log(`✅ ULRF: Successfully hydrated ${r}`)}catch(e){console.error(`❌ ULRF: Error hydrating ${r}:`,e)}}window.addEventListener("DOMContentLoaded",()=>{_()});
