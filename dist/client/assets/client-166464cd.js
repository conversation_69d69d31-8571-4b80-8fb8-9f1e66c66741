let R=new Set,F=!1;function $(t){R.add(t),F||(F=!0,requestAnimationFrame(()=>{F=!1;const r=Array.from(R);R.clear(),r.forEach(s=>s())}))}let w=null;function f(t){let r=t;const s=new Set;return[()=>(w&&s.add(w),r),n=>{n!==r&&(r=n,s.forEach(l=>$(l)))}]}function x(t){w=t,t(),w=null}const T=new EventTarget;function A(t){const[r,s]=f(t.initial??0),o=document.createElement("button");return o.textContent=String(r()),o.style.padding="10px 20px",o.style.fontSize="16px",o.style.backgroundColor="#007acc",o.style.color="white",o.style.border="none",o.style.borderRadius="4px",o.style.cursor="pointer",o.addEventListener("click",()=>s(r()+1)),x(()=>{o.textContent=`Count: ${r()}`}),o}function B(t){const[r,s]=f(t.name??"World"),o=document.createElement("div");o.style.padding="20px",o.style.border="1px solid #ccc",o.style.borderRadius="8px",o.style.backgroundColor="#f9f9f9",o.style.maxWidth="400px",o.style.margin="0 auto";const c=document.createElement("h3");c.textContent="Interactive Greeting",c.style.marginTop="0",c.style.color="#333";const n=document.createElement("input");n.type="text",n.value=r(),n.placeholder="Enter your name",n.style.padding="8px 12px",n.style.fontSize="14px",n.style.border="1px solid #ddd",n.style.borderRadius="4px",n.style.width="200px",n.style.marginRight="10px";const l=document.createElement("p");l.style.fontSize="18px",l.style.fontWeight="bold",l.style.color="#007acc",l.style.marginTop="15px";const e=document.createElement("button");return e.textContent="Broadcast Greeting",e.style.padding="8px 16px",e.style.fontSize="14px",e.style.backgroundColor="#28a745",e.style.color="white",e.style.border="none",e.style.borderRadius="4px",e.style.cursor="pointer",e.style.marginTop="10px",n.addEventListener("input",i=>{const a=i.target;s(a.value||"World")}),e.addEventListener("click",()=>{T.dispatchEvent(new CustomEvent("greetingBroadcast",{detail:{name:r(),message:`Hello, ${r()}!`}}))}),x(()=>{l.textContent=`Hello, ${r()}! 👋`}),o.appendChild(c),o.appendChild(n),o.appendChild(l),o.appendChild(e),o}function P(t){const[r,s]=f("No messages yet..."),[o,c]=f(0),n=document.createElement("div");n.style.padding="20px",n.style.border="2px solid #007acc",n.style.borderRadius="8px",n.style.backgroundColor="#e8f4fd",n.style.maxWidth="400px",n.style.margin="0 auto";const l=document.createElement("h3");l.textContent=t.title||"Event Listener",l.style.marginTop="0",l.style.color="#007acc";const e=document.createElement("div");e.style.padding="10px",e.style.backgroundColor="white",e.style.border="1px solid #ddd",e.style.borderRadius="4px",e.style.marginBottom="10px",e.style.minHeight="40px",e.style.fontSize="16px";const i=document.createElement("p");return i.style.fontSize="14px",i.style.color="#666",i.style.margin="0",T.addEventListener("greetingBroadcast",a=>{s(a.detail.message),c(o()+1)}),x(()=>{e.textContent=r()}),x(()=>{i.textContent=`Messages received: ${o()}`}),n.appendChild(l),n.appendChild(e),n.appendChild(i),n}function U(t){const[r,s]=f(!1),[o,c]=f("none"),n=document.createElement("div");n.style.padding="20px",n.style.border="1px solid #ddd",n.style.borderRadius="8px",n.style.backgroundColor="#fafafa",n.style.maxWidth="500px",n.style.margin="0 auto";const l=document.createElement("h3");l.textContent=t.title||"Image Filter (Web Worker Demo)",l.style.marginTop="0",l.style.color="#333";const e=document.createElement("canvas");e.width=200,e.height=150,e.style.border="1px solid #ccc",e.style.display="block",e.style.margin="10px auto";const i=e.getContext("2d");W(i,e.width,e.height);const a=document.createElement("div");a.style.display="flex",a.style.gap="10px",a.style.justifyContent="center",a.style.flexWrap="wrap",a.style.marginTop="15px";const p=document.createElement("p");p.style.textAlign="center",p.style.fontSize="14px",p.style.color="#666",p.style.marginTop="10px",[{name:"Original",value:"none"},{name:"Grayscale",value:"grayscale"},{name:"Invert",value:"invert"},{name:"Blur",value:"blur"}].forEach(y=>{const d=document.createElement("button");d.textContent=y.name,d.style.padding="8px 16px",d.style.fontSize="14px",d.style.border="1px solid #007acc",d.style.borderRadius="4px",d.style.cursor="pointer",d.style.backgroundColor="white",d.style.color="#007acc",d.addEventListener("click",()=>{r()||C(y.value)}),a.appendChild(d)}),x(()=>{r()?p.textContent=`Processing ${o()}...`:p.textContent=`Current filter: ${o()}`});async function C(y){if(y==="none"){W(i,e.width,e.height),c("none");return}s(!0),c(y);try{const d=i.getImageData(0,0,e.width,e.height),u=new Worker(new URL("/assets/imageProcessor-4d7f28aa.js",self.location),{type:"module"});u.postMessage({imageData:d,width:e.width,height:e.height,filter:y}),u.onmessage=m=>{const{processed:h}=m.data;i.putImageData(h,0,0),u.terminate(),s(!1)},u.onerror=m=>{console.error("Worker error:",m),u.terminate(),s(!1)}}catch(d){console.error("Filter error:",d),s(!1)}}return n.appendChild(l),n.appendChild(e),n.appendChild(a),n.appendChild(p),n}function W(t,r,s){const o=t.createLinearGradient(0,0,r,s);o.addColorStop(0,"#ff6b6b"),o.addColorStop(.25,"#4ecdc4"),o.addColorStop(.5,"#45b7d1"),o.addColorStop(.75,"#96ceb4"),o.addColorStop(1,"#ffeaa7"),t.fillStyle=o,t.fillRect(0,0,r,s),t.fillStyle="rgba(255, 255, 255, 0.8)",t.fillRect(20,20,60,40),t.fillStyle="rgba(0, 0, 0, 0.6)",t.beginPath(),t.arc(150,75,30,0,Math.PI*2),t.fill(),t.fillStyle="#333",t.font="16px Arial",t.textAlign="center",t.fillText("Test Image",r/2,s-20)}function H(t){const[r,s]=f(0),[o,c]=f(0),[n,l]=f(0),e=document.createElement("div");e.style.padding="15px",e.style.border="1px solid #28a745",e.style.borderRadius="8px",e.style.backgroundColor="#f8fff9",e.style.maxWidth="300px",e.style.margin="0 auto",e.style.fontSize="14px";const i=document.createElement("h4");i.textContent=t.title||"Performance Monitor",i.style.marginTop="0",i.style.marginBottom="15px",i.style.color="#28a745",i.style.textAlign="center";const a=document.createElement("div");a.style.display="grid",a.style.gridTemplateColumns="1fr 1fr",a.style.gap="10px";const p=document.createElement("div"),b=document.createElement("div");b.textContent="FPS:",b.style.fontWeight="bold",b.style.color="#666";const C=document.createElement("div");C.style.fontSize="18px",C.style.color="#28a745",C.style.fontWeight="bold",p.appendChild(b),p.appendChild(C);const y=document.createElement("div"),d=document.createElement("div");d.textContent="Memory:",d.style.fontWeight="bold",d.style.color="#666";const u=document.createElement("div");u.style.fontSize="18px",u.style.color="#007acc",u.style.fontWeight="bold",y.appendChild(d),y.appendChild(u);const m=document.createElement("div");m.style.gridColumn="1 / -1",m.style.textAlign="center",m.style.marginTop="10px";const h=document.createElement("div");h.textContent="Reactive Updates:",h.style.fontWeight="bold",h.style.color="#666";const v=document.createElement("div");v.style.fontSize="16px",v.style.color="#dc3545",v.style.fontWeight="bold",m.appendChild(h),m.appendChild(v);const g=document.createElement("div");g.style.gridColumn="1 / -1",g.style.marginTop="15px",g.style.padding="10px",g.style.backgroundColor="#e9ecef",g.style.borderRadius="4px",g.style.fontSize="12px",g.style.color="#495057",g.innerHTML=`
    <strong>Bundle Size:</strong> ~2.3KB gzipped<br>
    <strong>Runtime:</strong> Minimal overhead<br>
    <strong>Hydration:</strong> Selective islands
  `,a.appendChild(p),a.appendChild(y),a.appendChild(m),a.appendChild(g),e.appendChild(i),e.appendChild(a);let k=0,L=performance.now();function M(){k++;const E=performance.now();E-L>=1e3&&(s(Math.round(k*1e3/(E-L))),k=0,L=E),requestAnimationFrame(M)}M();function z(){if("memory"in performance){const E=performance.memory,I=Math.round(E.usedJSHeapSize/1024/1024);c(I)}}return setInterval(z,1e3),x(()=>{C.textContent=`${r()}`,l(n()+1)}),x(()=>{o()>0?u.textContent=`${o()}MB`:u.textContent="N/A",l(n()+1)}),x(()=>{v.textContent=`${n()}`}),e}const q={Counter:t=>A(t),Greeting:t=>B(t),EventListener:t=>P(t),ImageFilter:t=>U(t),PerformanceMonitor:t=>H(t)};function D(){console.log("🏝️ ULRF: Starting hydration...");const t=document.querySelectorAll("[data-ulrf-component]");console.log(`🏝️ ULRF: Found ${t.length} islands to hydrate`),t.forEach(r=>{switch(r.getAttribute("data-hydrate")||"load"){case"idle":"requestIdleCallback"in window?window.requestIdleCallback(()=>S(r)):setTimeout(()=>S(r),200);break;case"visible":new IntersectionObserver((n,l)=>{n.forEach(e=>{e.isIntersecting&&(l.disconnect(),S(r))})},{threshold:.1}).observe(r);break;case"event":const c=()=>{r.removeEventListener("click",c),S(r)};r.addEventListener("click",c);break;default:S(r);break}})}function S(t){const r=t.getAttribute("data-ulrf-component"),s=t.getAttribute("data-props")||"{}";console.log(`🏝️ ULRF: Hydrating ${r} with props:`,s);const o=JSON.parse(s.replace(/&quot;/g,'"')),c=r?q[r]:null;if(!c){console.warn(`❌ ULRF: No component registered for ${r}`);return}try{const n=c(o);t.replaceWith(n),console.log(`✅ ULRF: Successfully hydrated ${r}`)}catch(n){console.error(`❌ ULRF: Error hydrating ${r}:`,n)}}window.addEventListener("DOMContentLoaded",()=>{D()});
