(function(){"use strict";self.onmessage=async n=>{const{imageData:t,width:e,height:a,filter:s}=n.data;let r;switch(s){case"grayscale":r=b(t);break;case"invert":r=x(t);break;case"blur":r=A(t,e,a);break;default:r=t}self.postMessage({processed:r})};function b(n){const t=new Uint8ClampedArray(n.data);for(let e=0;e<t.length;e+=4){const a=(t[e]+t[e+1]+t[e+2])/3;t[e]=a,t[e+1]=a,t[e+2]=a}return new ImageData(t,n.width,n.height)}function x(n){const t=new Uint8ClampedArray(n.data);for(let e=0;e<t.length;e+=4)t[e]=255-t[e],t[e+1]=255-t[e+1],t[e+2]=255-t[e+2];return new ImageData(t,n.width,n.height)}function A(n,t,e){const a=new Uint8ClampedArray(n.data),s=new Uint8ClampedArray(n.data),r=2;for(let l=0;l<e;l++)for(let o=0;o<t;o++){let h=0,w=0,g=0,c=0;for(let i=-r;i<=r;i++)for(let f=-r;f<=r;f++){const u=l+i,y=o+f;if(u>=0&&u<e&&y>=0&&y<t){const p=(u*t+y)*4;h+=s[p],w+=s[p+1],g+=s[p+2],c++}}const d=(l*t+o)*4;a[d]=h/c,a[d+1]=w/c,a[d+2]=g/c}return new ImageData(a,n.width,n.height)}})();
