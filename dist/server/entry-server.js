import express from "express";
import path from "path";
import fs from "fs/promises";
import { fileURLToPath } from "url";
function renderIsland(componentName, props, hydrateStrategy = "load") {
  const serialized = JSON.stringify(props).replace(/"/g, "&quot;");
  return `<div data-ulrf-component="${componentName}" data-props="${serialized}" data-hydrate="${hydrateStrategy}"></div>`;
}
function renderHomePage(url) {
  let html = "";
  html += `<header style="padding: 20px; background: #f0f0f0; text-align: center;">`;
  html += `<h1>Welcome to ULRF</h1>`;
  html += `<p>Ultra-Light Reactive Frontend Framework</p>`;
  html += `<p style="font-size: 14px; color: #666;">Demonstrating Islands Architecture with Selective Hydration</p>`;
  html += `</header>`;
  html += `<main style="padding: 20px;">`;
  html += `<section style="margin-bottom: 40px; text-align: center;">`;
  html += `<h2>Counter Islands</h2>`;
  html += `<div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap; margin: 20px 0;">`;
  html += `<div style="text-align: center;">`;
  html += `<h4>Immediate Hydration</h4>`;
  html += renderIsland("Counter", { initial: 5 });
  html += `</div>`;
  html += `<div style="text-align: center;">`;
  html += `<h4>Hydrate When Visible</h4>`;
  html += renderIsland("Counter", { initial: 10 }, "visible");
  html += `</div>`;
  html += `<div style="text-align: center;">`;
  html += `<h4>Hydrate on Click</h4>`;
  html += renderIsland("Counter", { initial: 0 }, "event");
  html += `</div>`;
  html += `</div>`;
  html += `</section>`;
  html += `<section style="margin-bottom: 40px;">`;
  html += `<h2 style="text-align: center;">Event Bus Communication</h2>`;
  html += `<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; max-width: 900px; margin: 0 auto;">`;
  html += `<div>`;
  html += renderIsland("Greeting", { name: "ULRF User" });
  html += `</div>`;
  html += `<div>`;
  html += renderIsland("EventListener", { title: "Message Receiver" });
  html += `</div>`;
  html += `</div>`;
  html += `<p style="text-align: center; margin-top: 20px; font-size: 14px; color: #666;">`;
  html += `Type in the greeting input and click "Broadcast Greeting" to see cross-component communication!`;
  html += `</p>`;
  html += `</section>`;
  html += `<section style="margin-bottom: 40px;">`;
  html += `<h2 style="text-align: center;">Web Worker Integration</h2>`;
  html += `<div style="max-width: 600px; margin: 0 auto;">`;
  html += renderIsland("ImageFilter", { title: "Image Processing with Web Workers" }, "visible");
  html += `</div>`;
  html += `<p style="text-align: center; margin-top: 20px; font-size: 14px; color: #666;">`;
  html += `Image processing runs in a separate thread to avoid blocking the main UI!`;
  html += `</p>`;
  html += `</section>`;
  html += `</main>`;
  html += `<section style="margin-bottom: 40px;">`;
  html += `<h2 style="text-align: center;">Performance Monitoring</h2>`;
  html += `<div style="max-width: 400px; margin: 0 auto;">`;
  html += renderIsland("PerformanceMonitor", { title: "Real-time Performance" });
  html += `</div>`;
  html += `</section>`;
  html += `<footer style="padding: 20px; background: #f0f0f0; text-align: center; margin-top: 50px;">`;
  html += `<p>© 2025 ULRF Demo - Built with Vite</p>`;
  html += `<p style="font-size: 12px; color: #888;">Client bundle size: ~2.3KB gzipped | SSR + Islands Architecture</p>`;
  html += `</footer>`;
  return html;
}
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const app = express();
const port = process.env.PORT ? Number(process.env.PORT) : 3e3;
app.use("/assets", express.static(path.resolve(__dirname, "../client/assets")));
app.use(express.static(path.resolve(__dirname, "../client")));
async function getClientEntryFile() {
  try {
    const assetsDir = path.resolve(__dirname, "../client/assets");
    const files = await fs.readdir(assetsDir);
    const clientFile = files.find((file) => file.startsWith("client-") && file.endsWith(".js"));
    return clientFile ? `/assets/${clientFile}` : "/assets/client.js";
  } catch (err) {
    console.warn("Could not find client entry file, using fallback");
    return "/assets/client.js";
  }
}
app.get("*", async (req, res) => {
  try {
    const appHtml = renderHomePage(req.url);
    const indexHtmlPath = path.resolve(__dirname, "../index.html");
    let template = await fs.readFile(indexHtmlPath, "utf-8");
    const clientEntryFile = await getClientEntryFile();
    let html = template.replace("<!--ssr-outlet-->", appHtml);
    html = html.replace("/src/client/entry-client.ts", clientEntryFile);
    res.status(200).set({ "Content-Type": "text/html" }).send(html);
  } catch (err) {
    console.error(err);
    res.status(500).send("Internal Server Error");
  }
});
app.listen(port, () => {
  console.log(`ULRF Server running at http://localhost:${port}`);
});
