// Simple test script to verify ULRF framework is working
const puppeteer = require('puppeteer');

async function testFramework() {
  console.log('🧪 Testing ULRF Framework...');
  
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Listen for console messages
  page.on('console', msg => {
    if (msg.text().includes('ULRF:')) {
      console.log('📱', msg.text());
    }
  });
  
  try {
    // Navigate to the page
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    
    // Check if the page loaded
    const title = await page.title();
    console.log('✅ Page loaded:', title);
    
    // Check if islands are present
    const islands = await page.$$('[data-ulrf-component]');
    console.log(`🏝️ Found ${islands.length} islands before hydration`);
    
    // Wait a bit for hydration
    await page.waitForTimeout(2000);
    
    // Check if islands were hydrated (should be replaced)
    const remainingIslands = await page.$$('[data-ulrf-component]');
    console.log(`🏝️ Found ${remainingIslands.length} islands after hydration`);
    
    // Check if interactive elements exist
    const buttons = await page.$$('button');
    console.log(`🔘 Found ${buttons.length} interactive buttons`);
    
    // Test counter interaction
    if (buttons.length > 0) {
      const initialText = await page.evaluate(el => el.textContent, buttons[0]);
      console.log('🔢 Initial counter text:', initialText);
      
      await buttons[0].click();
      await page.waitForTimeout(100);
      
      const newText = await page.evaluate(el => el.textContent, buttons[0]);
      console.log('🔢 After click:', newText);
      
      if (initialText !== newText) {
        console.log('✅ Counter interaction working!');
      } else {
        console.log('❌ Counter interaction failed');
      }
    }
    
    console.log('🎉 Framework test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Run if puppeteer is available
if (typeof require !== 'undefined') {
  try {
    testFramework().catch(console.error);
  } catch (e) {
    console.log('⚠️ Puppeteer not available, skipping automated test');
    console.log('🌐 Please open http://localhost:3000 in your browser to test manually');
  }
} else {
  console.log('🌐 Please open http://localhost:3000 in your browser to test the framework');
}
