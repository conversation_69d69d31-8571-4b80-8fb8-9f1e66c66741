## Overview

This guide details the end-to-end implementation of a scalable, ultra-light reactive frontend framework (“ULRF”) powered by Vite. It covers every facet of the architecture we proposed—from reactive primitives and compile-time templating to server-side rendering (SSR), selective hydration (“islands”), microfrontend support, and offloading heavy work via Web Workers or WebAssembly. By following these steps, you will create a production-ready Vite project that emits minimal client code, hydrating only the interactive parts of your UI on demand. The intended audience is software engineering students and practitioners familiar with modern JavaScript development.

---

## Table of Contents

1. [Prerequisites and Tooling](#prerequisites-and-tooling)
2. [High-Level Architecture Recap](#high-level-architecture-recap)
3. [Project Initialization and Directory Structure](#project-initialization-and-directory-structure)
4. [Configuring Vite for Hybrid SSR + Client Bundles](#configuring-vite-for-hybrid-ssr--client-bundles)
5. [Implementing the Reactive Core](#implementing-the-reactive-core)
6. [Component Patterns and Template Compilation](#component-patterns-and-template-compilation)
7. [Server-Side Rendering Pipeline](#server-side-rendering-pipeline)
8. [Hydration and Islands Strategy](#hydration-and-islands-strategy)
9. [Microfrontend Integration](#microfrontend-integration)
10. [Offloading Work: Web Workers and WebAssembly](#offloading-work-web-workers-and-webassembly)
11. [Build, Deployment, and CDN Optimization](#build-deployment-and-cdn-optimization)
12. [Performance Considerations and Best Practices](#performance-considerations-and-best-practices)
13. [Developer Experience (DX) and Tooling](#developer-experience-dx-and-tooling)
14. [Conclusion](#conclusion)

---

## Prerequisites and Tooling

Before diving in, ensure you have the following installed and configured:

1. **Node.js (≥18 LTS)**
2. **npm or Yarn**
3. **Vite (≥4.x)** globally installed (`npm install -g vite`)
4. **TypeScript (≥4.5)** and associated type definitions (`@types/node`, `@vitejs/plugin-react`, etc.)
5. **Express (≥4.x)** or a comparable Node HTTP server for SSR (you can swap in Deno or Bun if preferred)
6. **ESLint + Prettier** (optional but recommended for code consistency)
7. **Git** (for version control and branching)

> **Note**: Although this guide uses **Express on Node.js** for the SSR example, you can adapt it to Deno or Bun by adjusting your server entry point and import patterns. All frontend code (components, reactive core, hydration) remains identical.

---

## High-Level Architecture Recap

At a glance, ULRF’s architecture consists of:

1. **Reactive Core**

   * **Signals** or `Proxy`-based primitives for fine-grained state tracking
   * **`effect()`** functions that synchronously subscribe to signals
   * **Event bus** (native `CustomEvent` / `EventTarget`) for cross-component communication
   * **`requestAnimationFrame` (rAF)** for batched writes

2. **Compile-Time Templating**

   * Two supported syntaxes:

     * **JSX (TSX)**
     * **HTML-template binding** (Vue-/Svelte-style)
   * Compiler transforms templates into direct DOM mutation code (no Virtual DOM)
   * Each “reactive point” is known at build time, so only minimal runtime is shipped

3. **Server-Side Rendering (SSR)**

   * Node (or Deno/Bun) runs `render()` functions per route
   * Emits static HTML with embedded **island placeholders** (e.g., `<div data-ulrf-component="Counter" data-props="…"></div>`)
   * Streams HTML to the client for immediate paint

4. **Hydration / Islands**

   * The HTML “just works” as static content; no JS is required for non-interactive parts
   * Each interactive **island** is a self-contained bundle that:

     1. Listens for user events (e.g., click) on its placeholder
     2. Lazy-loads the compiled component code on first interaction
     3. “Resumes” the reactive component without re-rendering everything

5. **Microfrontend Support**

   * Each island can be deployed and versioned independently
   * They register as **Web Components** (Custom Elements) or attach to DOM markers
   * Inter-island communication uses native `CustomEvent` events

6. **Offloading Heavy Work**

   * **Web Workers** for CPU-intensive tasks (image processing, data crunching)
   * **WebAssembly (WASM)** modules for numerically intensive kernels
   * Both integrate into the reactive core via signals or event listeners

7. **Build & Deployment**

   * **Vite** compiles and bundles client code (island entry points) into optimized chunks
   * **SSR bundle** is built separately (with `vite build --ssr …`)
   * **CDN** or **Edge network** distribution for static assets and HTML streaming

---

## Project Initialization and Directory Structure

1. **Create a new directory** for your project and initialize a Git repository:

   ```bash
   mkdir ulrf-vite-example
   cd ulrf-vite-example
   git init
   ```

2. **Initialize `package.json`** (or use `npm init` / `yarn init`):

   ```jsonc
   {
     "name": "ulrf-vite-example",
     "version": "0.1.0",
     "type": "module",
     "scripts": {
       "dev": "vite",
       "build": "vite build && vite build --ssr src/server/entry-server.ts",
       "start": "node dist/server/entry-server.js"
     },
     "dependencies": {
       "express": "^4.18.2"
     },
     "devDependencies": {
       "@types/node": "^18.11.9",
       "@vitejs/plugin-react": "^4.0.0",
       "typescript": "^4.8.4",
       "vite": "^4.0.0",
       "esbuild-register": "^3.1.1"
     }
   }
   ```

3. **Install dependencies**:

   ```bash
   npm install
   npm install --save-dev vite @vitejs/plugin-react typescript @types/node esbuild-register
   ```

4. **Add TypeScript configuration** (`tsconfig.json`):

   ```jsonc
   {
     "compilerOptions": {
       "target": "ESNext",
       "module": "ESNext",
       "moduleResolution": "Node",
       "jsx": "react-jsx",
       "strict": true,
       "esModuleInterop": true,
       "skipLibCheck": true,
       "outDir": "dist",
       "rootDir": "src",
       "resolveJsonModule": true
     },
     "include": ["src"]
   }
   ```

5. **Create the directory layout** under `src/`:

   ```
   src/
   ├── client/              # Client‐side entry points and hydration logic
   │   ├── entry-client.ts
   │   ├── hydrate.ts
   │   └── reactive.ts
   ├── components/          # UI components (JSX or HTML templates)
   │   └── Counter.tsx
   ├── server/              # Server‐side rendering code and Express setup
   │   ├── entry-server.ts
   │   └── render.ts
   └── index.html           # Template injected with SSR HTML
   ```

6. **Populate `index.html`** (in `src/index.html`):

   ```html
   <!DOCTYPE html>
   <html lang="en">
     <head>
       <meta charset="UTF-8" />
       <meta name="viewport" content="width=device-width, initial-scale=1.0" />
       <title>ULRF Vite Demo</title>
     </head>
     <body>
       <div id="app"><!--ssr-outlet--></div>
       <!-- We only load client hydration code AFTER SSR -->
       <script type="module" src="/src/client/entry-client.ts"></script>
     </body>
   </html>
   ```

---

## Configuring Vite for Hybrid SSR + Client Bundles

Vite can build both client and server bundles in one configuration. We’ll use a single `vite.config.ts` to define:

* A **client entry point** (`src/client/entry-client.ts`)
* The **SSR entry point** (`src/server/entry-server.ts`)
* Aliases for `@/` → `src/`
* React plugin for JSX/TSX support

Create `vite.config.ts` at project root:

```ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  // 1. Plugins
  plugins: [react()],

  // 2. Path aliases
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    }
  },

  // 3. Multiple build targets
  build: {
    // (a) Client‐side build
    rollupOptions: {
      input: {
        client: resolve(__dirname, 'src/client/entry-client.ts')
      }
    }
  },

  // 4. SSR configuration
  ssr: {
    // Tell Vite not to externalize the React plugin
    noExternal: ['@vitejs/plugin-react']
  }
});
```

With this config, running `vite build` produces two bundles:

1. `dist/client/entry-client.[hash].js` for client hydration
2. `dist/server/entry-server.js` for SSR

Use `npm run build` to compile both. Then serve the SSR bundle with Node/Express.

---

## Implementing the Reactive Core

At the heart of ULRF is a micro reactive system that ships \~1–2 KB of code. It consists of:

1. **`createSignal(initialValue)`**

   * Returns `[get, set]` functions
   * Maintains a set of subscribers (effects)

2. **`effect(fn)`**

   * Temporarily sets `currentSubscriber` to `fn`
   * Invokes `fn()` so any `get` calls register this subscriber
   * Clears `currentSubscriber` when done

3. **Batching updates via `requestAnimationFrame`** (optional next step)

4. **Global event bus** via a shared `EventTarget` instance for inter-component messages

Create `src/client/reactive.ts`:

```ts
// ----------------------------------
// src/client/reactive.ts
// ----------------------------------

export type Subscriber = () => void;

// A simple queue to batch updates via rAF
let queued = new Set<Subscriber>();
let rafScheduled = false;
function scheduleRaf(fn: Subscriber) {
  queued.add(fn);
  if (!rafScheduled) {
    rafScheduled = true;
    requestAnimationFrame(() => {
      rafScheduled = false;
      const toRun = Array.from(queued);
      queued.clear();
      toRun.forEach((sub) => sub());
    });
  }
}

// currentSubscriber is set inside effect()
let currentSubscriber: Subscriber | null = null;

// createSignal: maintains local state and subscribers
export function createSignal<T>(initial: T): [() => T, (newVal: T) => void] {
  let value = initial;
  const subs = new Set<Subscriber>();

  const get = (): T => {
    // If we’re inside an effect, register it
    if (currentSubscriber) {
      subs.add(currentSubscriber);
    }
    return value;
  };

  const set = (newVal: T) => {
    if (newVal === value) return;
    value = newVal;
    subs.forEach((sub) => scheduleRaf(sub));
  };

  return [get, set];
}

// effect: registers a function that reads signals
export function effect(fn: Subscriber) {
  currentSubscriber = fn;
  fn();
  currentSubscriber = null;
}

// Simple Event Bus for cross‐component communication
export const eventBus = new EventTarget();

// Example usage:
// eventBus.addEventListener('userLoggedIn', (e) => { … });
// eventBus.dispatchEvent(new CustomEvent('userLoggedIn', { detail: { user } }));
```

> **Key Points**
>
> * We schedule subscriber notifications via `requestAnimationFrame` to batch multiple signal updates into a single paint.
> * This ensures synchronous semantics at the JS level, yet avoids layout thrashing if two signals update in rapid succession.
> * The `eventBus` is a shared `EventTarget` instance so any component can subscribe or dispatch custom events.

---

## Component Patterns and Template Compilation

### 1. Component Folder Structure

All UI components live under `src/components/`. Each component exports a function that returns a DOM node (or a Web Component). We support two syntaxes:

* **JSX/TSX** for those familiar with React
* **HTML-like templates** (Svelte/Vue style) that compile to DOM manipulation code

#### Example: `src/components/Counter.tsx` (JSX Syntax)

```tsx
// ----------------------------------
// src/components/Counter.tsx
// ----------------------------------

import { createSignal, effect } from '../client/reactive';

interface CounterProps {
  initial?: number;
}

// Counter returns a <button> that displays and increments count
export function Counter(props: CounterProps) {
  const [count, setCount] = createSignal(props.initial ?? 0);

  // Create a DOM node
  const btn = document.createElement('button');
  btn.textContent = String(count());

  btn.addEventListener('click', () => setCount(count() + 1));

  // Reactive effect: whenever count() changes, update text
  effect(() => {
    btn.textContent = String(count());
  });

  return btn;
}
```

> **Behind the Scenes**
>
> * In production, we’ll compile JSX using Vite’s React plugin so that `<Counter />` can be used inside TSX.
> * Alternatively, a custom compiler can convert HTML templates into direct `createElement` / `.textContent` updates, but for now, we rely on JSX.

#### Example: `src/components/Greeting.html` (HTML Template Syntax)

```html
<!-- 
  This is an example of a “.html” component that our custom build
  step will compile. In a real project, we’d write a parser/plugin
  that identifies `{{name}}` or `@click="increment()"` and emits JS code.
-->
<div>
  <p>Hello, {{ name }}</p>
  <button @click="() => count++">Count: {{ count }}</button>
</div>
```

> **Custom Compiler**
>
> * You can build a Vite plugin or a separate CLI step that uses a small template parser (e.g., [lit-ssr](https://lit.dev/) or [marko](https://markojs.com/)) to transform these `.html` templates into vanilla JS render functions with reactive bindings.
> * The compiler scans for `{{ }}` interpolations, generates a `createSignal()` or `Proxy` mapping for each variable, and produces an `effect()`-wrapped DOM update.
> * Event attributes like `@click="…"` become `el.addEventListener('click', ...)`.

### 2. Compile-Time Rewriting

For fully optimized production builds, you will:

1. **Preprocess** your `.html` or `.tsx` files to identify reactive expressions.
2. **Generate** code that directly manipulates the DOM (no Virtual DOM).
3. **Tree-Shake** unused code—remove any features (e.g., SSR code) from the client bundle if they aren’t needed.

In Vite, this typically involves writing a custom plugin (e.g., `vite-plugin-ulrf`) that hooks into the `transform` step. For example:

```ts
// illustrative pseudo-code for a Vite plugin
import { Plugin } from 'vite';

export function ulrfTemplatePlugin(): Plugin {
  return {
    name: 'vite-plugin-ulrf-templates',
    enforce: 'pre',
    transform(code, id) {
      if (id.endsWith('.html')) {
        // 1. Parse HTML, find {{ }} and @click="" patterns
        // 2. Replace them with corresponding JS code
        // 3. Return a .js module string exporting a render() function
        const transformed = compileHtmlToJsModule(code);
        return {
          code: transformed,
          map: null
        };
      }
    }
  };
}
```

> **Note**: Implementing a full-fledged template compiler is beyond this guide’s scope, but you can start by adapting existing libraries like [lit-html](https://lit.dev/) or [svelte/compiler](https://github.com/sveltejs/svelte/tree/master/packages/svelte) to output minimal runtime code.

---

## Server-Side Rendering Pipeline

Our SSR pipeline uses Express on Node (but the same concepts apply to Deno/Bun). The goal is:

1. **Route matching**: For each incoming URL, determine which page/component to render.
2. **Render function**: Each page has a `render(url)` function that produces an HTML string containing island placeholders.
3. **Streaming**: Send the template (from `index.html`) to the client, injecting rendered HTML into `<!--ssr-outlet-->`.
4. **Embed State & Markers**: Serialized initial props (e.g., `{ initial: 5 }`) are embedded in `data-props` attributes on island elements.

### 1. SSR Entry Point (`src/server/entry-server.ts`)

```ts
// ----------------------------------
// src/server/entry-server.ts
// ----------------------------------
import express from 'express';
import path from 'path';
import fs from 'fs/promises';

// Import render function(s)
import { renderHomePage } from './render';

const app = express();
const port = process.env.PORT ? Number(process.env.PORT) : 3000;

// 1. Serve static assets (client bundles, CSS, images)
app.use(express.static(path.resolve(__dirname, '../public')));
app.use(express.static(path.resolve(__dirname, '../dist/client')));

// 2. Handle all GET requests
app.get('*', async (req, res) => {
  try {
    // 2a. Call your render function for this route
    const appHtml = renderHomePage(req.url);

    // 2b. Load the HTML template
    const indexHtmlPath = path.resolve(__dirname, '../index.html');
    let template = await fs.readFile(indexHtmlPath, 'utf-8');

    // 2c. Inject the rendered HTML into the template
    const html = template.replace('<!--ssr-outlet-->', appHtml);

    // 2d. Send the result
    res.status(200).set({ 'Content-Type': 'text/html' }).send(html);
  } catch (err) {
    console.error(err);
    res.status(500).send('Internal Server Error');
  }
});

// 3. Start server
app.listen(port, () => {
  console.log(`ULRF Server running at http://localhost:${port}`);
});
```

### 2. Render Functions (`src/server/render.ts`)

```ts
// ----------------------------------
// src/server/render.ts
// ----------------------------------
import { Counter } from '../components/Counter';

interface IslandPlaceholder {
  tag: string;           // e.g. 'div'
  componentName: string; // e.g. 'Counter'
  props: Record<string, any>;
}

function renderIsland(
  componentName: string,
  props: Record<string, any>
): string {
  // Serialize props (e.g. `{ initial: 5 }` → `{"initial":5}`)
  const serialized = JSON.stringify(props).replace(/"/g, '&quot;');
  return `<div data-ulrf-component="${componentName}" data-props="${serialized}"></div>`;
}

// Example: Home page render
export function renderHomePage(url: string): string {
  // In a real app, you might inspect `url` and choose different layouts
  // For demonstration, we just render a header, Counter island, and footer
  let html = '';
  html += `<header><h1>Welcome to ULRF</h1></header>`;
  html += `<main>`;
  // Render a Counter island with initial=5
  html += renderIsland('Counter', { initial: 5 });
  html += `</main>`;
  html += `<footer>© 2025 MyCompany</footer>`;
  return html;
}
```

> **Explanation**
>
> * `renderIsland()` produces a placeholder `<div>` tagged with `data-ulrf-component` so the client knows which JS to load.
> * Someday, you could extend this to output a `<counter-island>` Custom Element rather than a `div`. Then the client code calls `customElements.define('counter-island', CounterElement)` and hydration is straightforward.

---

## Hydration and Islands Strategy

### 1. Client Entry Point (`src/client/entry-client.ts`)

The client listens for `DOMContentLoaded` and calls a `hydrate()` function:

```ts
// ----------------------------------
// src/client/entry-client.ts
// ----------------------------------
import { hydrate } from './hydrate';

window.addEventListener('DOMContentLoaded', () => {
  hydrate();
});
```

### 2. Hydration Logic (`src/client/hydrate.ts`)

The goal is to find all island placeholders—elements marked with `data-ulrf-component`—and replace them with their live, reactive counterparts.

```ts
// ----------------------------------
// src/client/hydrate.ts
// ----------------------------------
import { Counter } from '../components/Counter';

// A registry mapping componentName → hydration function
const registry: Record<string, (props: any) => HTMLElement> = {
  Counter: (props: { initial?: number }) => {
    return Counter(props);
  }
  // e.g.   Greeting: (props) => GreetingComponent(props), etc.
};

export function hydrate() {
  // Select all elements with a data-ulrf-component attribute
  const islands = document.querySelectorAll<HTMLElement>(
    '[data-ulrf-component]'
  );

  islands.forEach((placeholder) => {
    const componentName = placeholder.getAttribute('data-ulrf-component');
    const propsJson = placeholder.getAttribute('data-props') || '{}';
    const props = JSON.parse(
      propsJson.replace(/&quot;/g, '"')
    );

    // Find the correct hydration function in the registry
    const hydrator = componentName ? registry[componentName] : null;
    if (!hydrator) {
      console.warn(`No component registered for ${componentName}`);
      return;
    }

    // Instantiate the actual component (this invokes its reactive core)
    const liveNode = hydrator(props);

    // Replace the placeholder in the DOM
    placeholder.replaceWith(liveNode);
  });
}
```

> **Hydration Triggers**
>
> * By default, the above code hydrates *all* islands on `DOMContentLoaded`. That may not be optimal for large pages.
> * For **deferred** hydration, you can add data attributes like `data-hydrate="idle"` or `data-hydrate="visible"`. The client code then checks those flags:
>
>   * **“idle”**: Use `requestIdleCallback()` (or a `setTimeout` fallback) to wait until the browser is idle
>   * **“visible”**: Use an `IntersectionObserver` to hydrate only when the island scrolls into view
>   * **“event”**: Attach a one-time event listener (e.g., “click”) on the placeholder that lazy-loads the island

#### Deferred Hydration Example

```ts
// ----------------------------------
// src/client/hydrate.ts (excerpt)
// ----------------------------------

export function hydrate() {
  const islands = document.querySelectorAll<HTMLElement>(
    '[data-ulrf-component]'
  );

  islands.forEach((placeholder) => {
    const hydrateStrategy = placeholder.getAttribute('data-hydrate') || 'load';
    switch (hydrateStrategy) {
      case 'idle':
        // Wait for idle period
        if ('requestIdleCallback' in window) {
          (window as any).requestIdleCallback(() => doHydrate(placeholder));
        } else {
          setTimeout(() => doHydrate(placeholder), 200);
        }
        break;
      case 'visible':
        const observer = new IntersectionObserver(
          (entries, obs) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                obs.disconnect();
                doHydrate(placeholder);
              }
            });
          },
          { threshold: 0.1 }
        );
        observer.observe(placeholder);
        break;
      case 'event':
        // Wait for a click (or any event type you choose)
        const handler = () => {
          placeholder.removeEventListener('click', handler);
          doHydrate(placeholder);
        };
        placeholder.addEventListener('click', handler);
        break;
      default:
        // 'load': hydrate immediately
        doHydrate(placeholder);
        break;
    }
  });
}

function doHydrate(placeholder: HTMLElement) {
  const componentName = placeholder.getAttribute('data-ulrf-component')!;
  const propsJson = placeholder.getAttribute('data-props')!;
  const props = JSON.parse(propsJson.replace(/&quot;/g, '"'));
  const hydrator = registry[componentName];
  if (!hydrator) {
    console.warn(`No component registered for ${componentName}`);
    return;
  }
  const liveNode = hydrator(props);
  placeholder.replaceWith(liveNode);
}
```

> **Benefits**
>
> * **Reduced JS on initial load**: Interactive code only runs when needed
> * **Improved TTI**: Non-interactive page parts don’t block hydration
> * **Fine-grained control**: You decide per-island when to hydrate

---

## Microfrontend Integration

The “island” concept inherently supports microfrontends: each island is a self-contained widget that can be versioned and deployed independently. Here’s how to wire everything:

1. **Define Each Micro-App as a Separate Package**

   * Example:

     ```
     packages/
     ├── header/
     │   ├── package.json
     │   ├── src/
     │   │   ├── Header.tsx
     │   │   └── index.ts
     │   └── vite.config.ts
     ├── counter-widget/
     │   ├── package.json
     │   └── src/
     │       └── Counter.tsx
     └── main-app/
         ├── package.json
         └── src/
             ├── entry-client.ts
             └── ...
     ```
   * Each package has its own Vite config and build process. The `main-app` can consume each micro-app as an NPM (or local) dependency.

2. **Publish or Link Packages**

   * Use `npm publish` / `npm link` / `yarn link` so that `main-app` can import micros via `import { Counter } from 'counter-widget'`.

3. **Expose Island Placeholders**

   * In `main-app`’s SSR `render()` function, import micro-apps and render placeholders:

     ```ts
     import { renderCounterIsland } from 'counter-widget/server';
     export function renderHomePage(url: string): string {
       let html = `<header>...</header>`;
       html += `<main>${renderCounterIsland({ initial: 10 })}</main>`;
       html += `<footer>...</footer>`;
       return html;
     }
     ```
   * Each micro-app’s `server/index.ts` exports a function that calls `renderIsland('Counter', props)` with appropriate component name and props.

4. **Client Bootstrapping**

   * `main-app`’s client hydration code sees `<div data-ulrf-component="Counter" …>` and dynamically loads the micro-app’s client bundle (e.g., `/node_modules/counter-widget/dist/client.js`) to produce the live widget.
   * You can implement dynamic imports in the hydration registry:

     ```ts
     const registry: Record<string, (props: any) => Promise<HTMLElement>> = {
       Counter: async (props) => {
         // Lazy load micro-app’s client bundle
         const module = await import('counter-widget');
         return module.Counter(props);
       }
     };
     ```
   * The first time `Counter` is hydrated, it fetches its own JS chunk straight from a CDN or local host.

5. **Shared Services & Event Bus**

   * If multiple microfrontends need to share auth or data services, publish them as a separate `shared-services` package. For example:

     ```ts
     // shared-services/auth.ts
     import { eventBus } from 'ulrf-reactive-core';
     export function login(user) {
       // ... authenticate user
       eventBus.dispatchEvent(new CustomEvent('userLoggedIn', { detail: user }));
     }

     export function onUserLoggedIn(handler: (user: any) => void) {
       eventBus.addEventListener('userLoggedIn', (e) => handler((e as CustomEvent).detail));
     }
     ```
   * Any island can import `onUserLoggedIn` to reactively update its UI when a user logs in.

6. **Deployment Strategies**

   * Each micro-app can be deployed independently to its own CDN path (e.g., `https://cdn.myapp.com/counter-widget/v1.2.3/client.js`).
   * The `main-app` SSR code references the exact versioned URL in the `data-script-src` or uses dynamic imports that resolve to correct URLs at runtime.

> **Result**: You achieve full **microfrontend** capabilities while keeping each widget’s runtime minimal. Only the islands needed by a given page are loaded.

---

## Offloading Work: Web Workers and WebAssembly

For CPU-intensive or long-running tasks that could block the main thread (e.g., image processing, data compression, large array computations), you can offload to **Web Workers** or **WebAssembly**. Below are steps to integrate both in ULRF.

### 1. Web Workers

1. **Create a Worker Script**

   * Put worker code under `src/workers/`. For example, `src/workers/imageProcessor.ts`:

     ```ts
     // ----------------------------------
     // src/workers/imageProcessor.ts
     // ----------------------------------
     // This code runs in a separate thread
     self.onmessage = async (e) => {
       const { imageData, width, height } = e.data;
       // Perform CPU-intensive processing (e.g., grayscale)
       for (let i = 0; i < imageData.data.length; i += 4) {
         const avg =
           (imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2]) / 3;
         imageData.data[i] = avg;
         imageData.data[i + 1] = avg;
         imageData.data[i + 2] = avg;
       }
       // Post the processed image data back to main thread
       self.postMessage({ processed: imageData });
     };
     ```

2. **Configure Vite to Bundle Workers**

   * Vite can handle Workers via special import syntax. In your component:

     ```ts
     // ----------------------------------
     // src/components/ImageFilter.ts
     // ----------------------------------
     export function ImageFilterComponent(canvas: HTMLCanvasElement) {
       const ctx = canvas.getContext('2d')!;
       
       async function processImage() {
         const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
         // Dynamically import a Worker
         const worker = new Worker(
           new URL('../workers/imageProcessor.ts', import.meta.url),
           { type: 'module' }
         );
         worker.postMessage({
           imageData,
           width: canvas.width,
           height: canvas.height
         });
         worker.onmessage = (e) => {
           const { processed } = e.data;
           ctx.putImageData(processed, 0, 0);
           worker.terminate();
         };
       }
       
       const btn = document.createElement('button');
       btn.textContent = 'Apply Filter';
       btn.addEventListener('click', () => processImage());
       return btn;
     }
     ```
   * Vite automatically bundles `imageProcessor.ts` as a separate worker chunk. At runtime, the Worker is instantiated by passing its module URL.

3. **Hydrate Worker-Enabled Islands**

   * In SSR, output a placeholder `<div>` that eventually mounts `ImageFilterComponent`:

     ```ts
     renderIsland('ImageFilterComponent', {});
     ```
   * The hydration code dynamically imports the island’s bundle, including the Worker import. Vite rewrites `new URL('../workers/imageProcessor.ts', import.meta.url)` to point to the hashed worker file in `dist/client`.

### 2. WebAssembly (WASM)

1. **Compile Rust/C/C++ to WASM**

   * Suppose you have a Rust crate `wasm-kernels` with a function `fn compute(data: &[u8]) -> Vec<u8>`. Use `wasm-pack` or `wasm-bindgen` to generate a JS/WASM bundle:

     ```bash
     cd wasm-kernels
     wasm-pack build --target web --out-dir dist
     ```
   * This produces `pkg/wasm_kernels_bg.wasm` and `pkg/wasm_kernels.js`.

2. **Load WASM in the Component**

   * In your component:

     ```ts
     // ----------------------------------
     // src/components/DataCruncher.ts
     // ----------------------------------
     import init, { compute } from '../../wasm-kernels/pkg/wasm_kernels.js';

     export async function DataCruncherComponent(data: Uint8Array) {
       // (1) Load and initialize the WASM module
       await init();
       // (2) Call the compute() function exported from Rust
       const result = compute(data);
       // ... use `result` to update the UI or signal further processing
       
       const pre = document.createElement('pre');
       pre.textContent = `Result length: ${result.length}`;
       return pre;
     }
     ```
   * Vite sees the `import init from '.../pkg/wasm_kernels.js'` and knows to copy the WASM file to `dist/assets/` and adjust the path.

3. **Lazy-Load WASM for Islands**

   * In SSR, output `<div data-ulrf-component="DataCruncher" data-props="…"></div>`.
   * On hydration, when `DataCruncherComponent` is instantiated, it lazy-loads the WASM module only if needed (on user interaction or idle). This keeps initial JS small.

> **Tip**: For both Workers and WASM, only bundle them in islands that require heavy work. This ensures your main bundle remains minimal.

---

## Build, Deployment, and CDN Optimization

### 1. Building Your Project

1. **Client Build**

   ```bash
   npm run build
   ```

   * Vite bundles all client entry points (e.g., `entry-client.ts`, plus all dynamically imported islands).
   * Creates `dist/client/` containing hashed JS/CSS/chunk files.

2. **Server Build**

   ```bash
   npm run build
   ```

   * Because our `package.json` script runs `vite build --ssr src/server/entry-server.ts`, Vite creates a server bundle at `dist/server/entry-server.js`.
   * This bundle can be run directly with `node` (or `deno run`, or `bun run` if you configured accordingly).

### 2. Serving and Deployment

1. **Local Testing**

   ```bash
   npm run start
   ```

   * This starts Express listening on `http://localhost:3000`.
   * Ensure you map `dist/client` and `public` as static folder(s) in your Express setup.

2. **Production Deployment**

   * **Option A**: Deploy to a traditional Node host (Heroku, AWS EC2, DigitalOcean, etc.)

     * Copy `dist/`, `package.json`, and `package-lock.json` to your server.
     * Run `npm install --production`.
     * Start with `node dist/server/entry-server.js`.
   * **Option B**: Deploy to an **Edge/Serverless** environment (Cloudflare Workers, Vercel, Netlify)

     * **Edge Wrapping**: Instead of Express, write a thin Edge handler (e.g., [Cloudflare’s HTTP handler](https://developers.cloudflare.com/workers/runtime-apis/fetch-event/)) that imports `dist/server/entry-server.js` and invokes your render function.
     * **Streaming HTML**: Many Edge providers support streaming responses, so you can call `renderHomePage()` and stream chunks of HTML.

3. **CDN Configuration**

   * After building, all of `dist/client`’s static assets (JS/CSS/WASM/Worker files) should be uploaded to a CDN or static host (e.g., AWS S3 + CloudFront, Netlify, Vercel’s Asset CDN).
   * In your SSR HTML template, reference static assets with absolute URLs (e.g., `https://cdn.mycdn.com/ulrf/v1.0.0/entry-client.[hash].js`).
   * Alternatively, use a dynamic import map at runtime so that client hydration code knows exactly where to fetch islands’ JS/WASM bundles.

### 3. Versioning and Cache Busting

* Leverage Vite’s default hashing (`[hash]`) so that every rebuild changes file names if contents differ.
* Use long-term caching on the CDN (e.g., `Cache-Control: max-age=31536000, immutable`) for hashed assets.
* Serve the SSR HTML with `Cache-Control: no-cache` or a short TTL, so that new deployments immediately reflect in the page’s `<script src="…">` tags.

---

## Performance Considerations and Best Practices

1. **Minimize Initial JS**

   * Our approach pushes virtually all interactivity off the initial HTML payload. Only an \~1 KB client runtime (signals + hydrate.js) plus per-island stubs are loaded.
   * Defer island hydration when possible via `data-hydrate="idle" / "visible"`.

2. **Fine-Grained Reactivity**

   * Because each signal subscription is tracked at a per-property level, updates only recompute the minimal necessary DOM mutations.
   * Avoid deep nested objects for your application state if you need extremely fast updates; consider splitting state into multiple signals.

3. **Batch DOM Writes**

   * We schedule all signal-derived reruns inside a `requestAnimationFrame` callback. This avoids layout thrashing and ensures that if multiple signals update in the same JavaScript tick, the DOM only reflows once per frame.

4. **Use Web Workers / WASM for Heavy Work**

   * Offload CPU-heavy tasks to Web Workers so that the main thread never has its FPS drop below 60.
   * Integrate your compute-intensive logic as WASM modules (e.g., data encryption, image filters, array processing).

5. **HTTP/2 & Code Splitting**

   * Serve your assets over HTTP/2 (or HTTP/3) so that many small chunks (islands) are fetched in parallel without head-of-line blocking.
   * Vite’s chunk splitting ensures that each island and worker is its own file. Don’t bundle everything into a single megabyte file.

6. **Prefetch & Preload Hints**

   * Use `<link rel="prefetch" href="…">` or `<link rel="preload" href="…">` in your SSR HTML for islands you predict the user will need soon (e.g., the “Search” island if the user hovers over the search bar).
   * Only prefetch critical islands (over-prefetching wastes bandwidth).

7. **Use HTTP Cache-Control Wisely**

   * Static assets: `cache-control: max-age=31536000, immutable`.
   * SSR pages: `cache-control: no-cache` or short TTL, because the HTML changes frequently.
   * API endpoints (for data): use appropriate caching headers (ETags, stale-while-revalidate, etc.).

8. **Size of the Reactive Core**

   * Keep your signal implementation under 2 KB minified.
   * Drop debugging or environment checks in production builds.
   * Consider using \[esbuild] or \[Terser] minification to aggressively strip out unused code.

---

## Developer Experience (DX) and Tooling

1. **Project Scaffold CLI**

   * Provide a simple CLI (`npx create-ulrf`) that sets up a bare-bones ULRF + Vite project with all conventions.
   * The CLI should:

     * Generate `vite.config.ts`, `tsconfig.json`, and directory structure
     * Install dependencies (`npm install`)
     * Create sample `Counter` and `DataCruncher` components

2. **Hot Module Replacement (HMR)**

   * Vite’s development server automatically supports HMR for your `.tsx` components and `.html` templates.
   * Because we use compile-time templates, HMR will recompile and patch only the affected islands or signal code in the client.

3. **DevTools Integration**

   * Ensure that `createSignal` and `effect` have helpful names in dev mode (e.g., attach `fn.name` to subscribers).
   * Provide a simple browser extension or `window.ulrfDev` API to inspect signals, see subscription graphs, and log updates.
   * Example:

     ```ts
     // In reactive.ts, only in development
     export function effect(fn: Subscriber) {
       currentSubscriber = fn;
       fn();
       currentSubscriber = null;
       if (import.meta.env.DEV) {
         console.debug('Registered effect:', fn.name);
       }
     }
     ```

4. **TypeScript Support**

   * All components and reactive tools should be fully typed. For instance, make `createSignal<T>` generic and indicate that `get()` returns `T`.
   * Provide intersection types for JSX components: `interface CounterProps { initial?: number; }` so that usage in TSX has autocompletion.
   * If you implement a custom HTML-template syntax, ship a `.d.ts` type definition so that TS/IDE can catch mistakes.

5. **Linting and Formatting**

   * Configure ESLint with recommended rules for modern JS/TS. Enforce no unused variables, strict null checks, etc.
   * Prettier for consistent formatting (tabs vs. spaces, single vs. double quotes).
   * Use Husky/Git hooks to run linting before commits.

6. **Testing Strategy**

   * **Unit Tests**: Test your reactive core (createSignal, effect) with a testing framework like Jest or Vitest.
   * **Component Tests**: Use [@testing-library/dom](https://testing-library.com/docs/dom-testing-library/intro) to mount components in a JSDOM environment and assert on their behavior.
   * **E2E Tests**: Cypress or Playwright to spin up the SSR server and navigate pages, verifying hydration success, island loading, and UI interactivity.
   * **Snapshot Tests**: For compiled output, capture generated HTML and ensure critical elements remain stable.

---

## Conclusion

By following these steps, you will have built a full-scale Vite-based architecture that:

* **Emits minimal client code** (≈1–2 KB core)
* **Streams fully rendered HTML** from a Node (or Deno/Bun) SSR pipeline
* **Implements fine-grained reactivity** via `Proxy`/`createSignal` + `effect`, with microtask-safe, rAF-driven batching
* **Supports islands hydration**: interactive widgets only load code on demand (idle, visible, or event-driven)
* **Facilitates microfrontend development**: each island can be a separate package, versioned and deployed independently
* **Offloads computationally expensive tasks** to Web Workers or WebAssembly
* **Optimizes build output** via Vite’s code splitting, hashed assets, and tree shaking

This approach combines the best of Qwik’s resumable SSR, Solid’s fine-grained signals, Astro’s islands, and Svelte/Marko’s compile-time optimization. The result is a framework that scales easily—from tiny widget-only pages (e.g., a single counter) to complex SPAs with dozens of islands—while delivering near-instant load times and extremely low runtime overhead on the client. Happy building!
