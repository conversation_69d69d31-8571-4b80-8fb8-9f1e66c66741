# ULRF Developer Guide

## Quick Start

### 1. Creating Your First Component

```typescript
import { defineComponent, useState, createElement } from '../client/component';

interface MyComponentProps {
  title?: string;
  initialValue?: number;
}

export const MyComponent = defineComponent<MyComponentProps>({
  template: (props, state, methods) => {
    // Create reactive state
    const count = useState(props.initialValue ?? 0);
    
    // Create DOM elements
    const container = createElement('div', {
      style: 'padding: 20px; border: 1px solid #ccc;'
    });
    
    const title = createElement('h3', {}, [props.title || 'My Component']);
    const display = createElement('p', {}, [() => `Count: ${count.value}`]);
    const button = createElement('button', {}, ['Click me']);
    
    // Add event listener
    button.addEventListener('click', () => {
      count.value++;
      methods.emit('countChanged', count.value);
    });
    
    // Assemble component
    container.appendChild(title);
    container.appendChild(display);
    container.appendChild(button);
    
    return container;
  }
});
```

### 2. Register Your Component

Add your component to the hydration registry:

```typescript
// src/client/hydrate.ts
import { MyComponent } from '../components/MyComponent';

const registry: Record<string, (props: any) => HTMLElement> = {
  MyComponent: (props) => MyComponent(props),
  // ... other components
};
```

### 3. Render on Server

Add your component to the server render function:

```typescript
// src/server/render.ts
export function renderHomePage(url: string): string {
  let html = '';
  // ... other content
  html += renderIsland('MyComponent', { 
    title: 'Hello World', 
    initialValue: 5 
  }, 'load'); // or 'visible', 'event', 'idle'
  return html;
}
```

## Core Concepts

### Reactive State

Use `useState` for reactive values:

```typescript
const count = useState(0);
count.value++; // Updates automatically trigger re-renders
console.log(count.value); // Read current value
```

### Computed Values

Use `useComputed` for derived state:

```typescript
const count = useState(0);
const doubled = useComputed(() => count.value * 2);
// doubled automatically updates when count changes
```

### Effects

Use `useEffect` for side effects:

```typescript
const name = useState('');

useEffect(() => {
  console.log('Name changed to:', name.value);
  // Optional cleanup function
  return () => {
    console.log('Cleaning up effect');
  };
}, [() => name.value]); // Dependencies
```

### Event Communication

Components can communicate via events:

```typescript
// Emit events
methods.emit('userLoggedIn', { userId: 123 });

// Listen for events
methods.listen('userLoggedIn', (data) => {
  console.log('User logged in:', data.userId);
});
```

### Styled Components

Create reusable styled elements:

```typescript
import { styled } from '../client/component';

const Button = styled('button', {
  padding: '10px 20px',
  backgroundColor: '#007acc',
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  cursor: 'pointer'
});

// Use in template
const myButton = Button({}, ['Click me']);
```

## Hydration Strategies

### Immediate (`load`)
Component hydrates as soon as the page loads:
```typescript
renderIsland('MyComponent', props, 'load');
```

### When Visible (`visible`)
Component hydrates when it scrolls into view:
```typescript
renderIsland('MyComponent', props, 'visible');
```

### On Interaction (`event`)
Component hydrates when user clicks on it:
```typescript
renderIsland('MyComponent', props, 'event');
```

### When Idle (`idle`)
Component hydrates when browser is idle:
```typescript
renderIsland('MyComponent', props, 'idle');
```

## Best Practices

### 1. Keep Components Small
- Each component should have a single responsibility
- Break complex components into smaller ones
- Use composition over inheritance

### 2. Minimize State
- Only make values reactive if they need to trigger updates
- Use local variables for non-reactive data
- Prefer computed values over manual state management

### 3. Use Appropriate Hydration Strategies
- `load`: Critical interactive elements (navigation, forms)
- `visible`: Below-the-fold content (image galleries, comments)
- `event`: Optional features (tooltips, modals)
- `idle`: Background features (analytics, prefetching)

### 4. Handle Cleanup
- Remove event listeners in unmounted lifecycle
- Clean up intervals and timeouts
- Cancel pending requests

### 5. Performance Tips
- Batch state updates when possible
- Use `useComputed` for expensive calculations
- Avoid creating functions inside render loops
- Use Web Workers for heavy computations

## Advanced Patterns

### Custom Hooks

Create reusable logic:

```typescript
function useCounter(initial = 0) {
  const count = useState(initial);
  
  const increment = () => count.value++;
  const decrement = () => count.value--;
  const reset = () => count.value = initial;
  
  return { count: count.value, increment, decrement, reset };
}
```

### Component Communication

```typescript
// Parent component
export const Parent = defineComponent({
  template: (props, state, methods) => {
    methods.listen('childEvent', (data) => {
      console.log('Received from child:', data);
    });
    
    // ... render children
  }
});

// Child component
export const Child = defineComponent({
  template: (props, state, methods) => {
    const button = createElement('button', {}, ['Notify Parent']);
    button.addEventListener('click', () => {
      methods.emit('childEvent', { message: 'Hello from child!' });
    });
    return button;
  }
});
```

### Error Boundaries

```typescript
export const ErrorBoundary = defineComponent({
  template: (props, state, methods) => {
    const hasError = useState(false);
    const error = useState<Error | null>(null);
    
    if (hasError.value) {
      return createElement('div', {
        style: 'color: red; padding: 20px; border: 1px solid red;'
      }, [
        'Something went wrong: ',
        error.value?.message || 'Unknown error'
      ]);
    }
    
    try {
      return props.children();
    } catch (err) {
      hasError.value = true;
      error.value = err as Error;
      return createElement('div', {}, ['Error occurred']);
    }
  }
});
```

## Debugging

### Enable Debug Mode

Add debug logging to your components:

```typescript
const DEBUG = import.meta.env.DEV;

if (DEBUG) {
  console.log('Component props:', props);
  console.log('Component state:', state);
}
```

### Performance Monitoring

Use the built-in PerformanceMonitor component or create custom metrics:

```typescript
const startTime = performance.now();
// ... component logic
const endTime = performance.now();
console.log(`Component render took ${endTime - startTime}ms`);
```

## Migration from Other Frameworks

### From React

```typescript
// React
const [count, setCount] = useState(0);

// ULRF
const count = useState(0);
// Use count.value instead of count
// Use count.value = newValue instead of setCount(newValue)
```

### From Vue

```typescript
// Vue
const count = ref(0);

// ULRF
const count = useState(0);
// Similar reactive behavior, use count.value
```

### From Svelte

```typescript
// Svelte
let count = 0;
$: doubled = count * 2;

// ULRF
const count = useState(0);
const doubled = useComputed(() => count.value * 2);
```
